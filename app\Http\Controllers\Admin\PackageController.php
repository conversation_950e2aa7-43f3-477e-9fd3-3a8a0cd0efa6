<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\PackageDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Create\CreatePackageRequest;
use App\Http\Requests\Admin\Update\UpdatePackageRequest;
use App\Repositories\PackageRepository;

class PackageController extends Controller
{
    private PackageRepository $package;

    public function __construct(
        PackageRepository $packageRepository,
    ) {
        $this->package = $packageRepository;
    }

    public function index(PackageDataTable $packageDataTable)
    {
        return $packageDataTable->render('admin.package.index');
    }

    public function store(CreatePackageRequest $request): \Illuminate\Http\JsonResponse
    {
        $input = $request->all();
        $query = $this->package->exitsColumn('name', $input['name']);
        if ($query) {
            return $this->error('Đã có tên gói này rồi');
        }
        $query = $this->package->create($input);
        if ($query) {

            return $this->success('Thêm gói thành công');
        }

        return $this->error('');
    }

    public function show(int $id): \Illuminate\Http\JsonResponse
    {
        $response = $this->package->find($id);
        if ($response) {
            return $this->success(data: $response);
        }

        return $this->error();
    }

    public function update(int $id, UpdatePackageRequest $request): \Illuminate\Http\JsonResponse
    {
        $input = $request->all();
        $query = $this->package->exitsKeyNot($id, 'name', $input['name']);
        if ($query) {
            return $this->error('Đã có gói có tên này! Vui lòng chọn tên khác');
        }
        $query = $this->package->find($id);
        if ($query) {
            $query->update($input);

            return $this->success('Cập nhật gói thành công');
        }

        return $this->error();
    }

    public function destroy(int $id): \Illuminate\Http\JsonResponse
    {
        $query = $this->package->delete($id);
        if ($query) {

            return $this->success('Xoá gói thành công');
        }

        return $this->error();
    }
}
