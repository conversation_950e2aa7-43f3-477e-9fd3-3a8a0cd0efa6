<div class="modal fade" id="kt_modal_new2" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered mw-650px">
        <div class="modal-content">
            <form class="form" method="POST" action="{{ route('user.topup.create') }}" id="kt_modal_new_form">
                @csrf
                <div class="modal-header" id="kt_modal_new_header">
                    <h2>Tạo mã chuyển khoản</h2>
                    <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                        <i class="ki-outline ki-cross fs-1"></i>
                    </div>
                </div>
                <div class="modal-body py-10 px-lg-17">
                    <div class=" me-n7 pe-7" id="kt_modal_new_scroll" data-kt-scroll="true"
                         data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto"
                         data-kt-scroll-dependencies="#kt_modal_new_header"
                         data-kt-scroll-wrappers="#kt_modal_new_scroll" data-kt-scroll-offset="300px">
                        <div class="d-flex flex-column mb-5 fv-row">
                            <label class="fs-5 fw-semibold mb-2">Số tiền<span style="color: red">*</span>:</label>
                            <input type="text" class="form-control form-control-solid" placeholder="" name="amount" id="amount" />
                        </div>
                        <div class="d-flex flex-column fv-row">
                            <label class="fs-5 fw-semibold mb-2">Nội dung chuyển tiền<span style="color: red">*</span>:</label>
                            <input readonly class="form-control form-control-solid" placeholder="" name="code" id="code" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer flex-center">
                    <button type="submit" class="btn btn-primary mr-2">{{ __('Thêm mới') }}</button>
                    <button type="reset" class="btn btn-secondary">{{ __('Nhập lại') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
