<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rent_package_proxy', function (Blueprint $table) {
            $table->unsignedBigInteger('rent_package_id');
            $table->unsignedBigInteger('proxy_id');

            $table->foreign('rent_package_id')->references('id')->on('rent_packages')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreign('proxy_id')->references('id')->on('proxies')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->primary(['rent_package_id', 'proxy_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rent_package_proxy');
    }
};
