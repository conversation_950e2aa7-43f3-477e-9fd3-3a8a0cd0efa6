<?php

namespace App\Http\Controllers\User;

use App\DataTables\UserVpnDataTable;
use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\Package;
use App\Models\RentPackage;
use App\Models\User;
use App\Services\BalanceService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class VpnController extends Controller
{
    private BalanceService $balance;

    public function __construct(BalanceService $balanceService)
    {
        $this->balance = $balanceService;
    }

    public function index(UserVpnDataTable $userVpnDataTable)
    {
        return $userVpnDataTable->render('user.vpn.index');
    }

    public function extend($id)
    {
        // $id = rent_package_id
        $user_id = Auth::id();
        try {
            $rent_package = RentPackage::findOrFail($id);
            $package = Package::findOrFail($rent_package->package_id);
            $user = User::findOrFail($user_id);
            if ($user->balance < $package->price) {
                return response()->json(['status' => false, 'title' => 'Lỗi', 'message' => 'Xin lỗi, tài khoản không đủ tiền']);
            }
            $expired = \Illuminate\Support\Carbon::parse($rent_package['expired_at']);
            //Hiện tại < hạn thuê
            if (Carbon::now()->lte($expired)) {
                $expiredDate = $expired->addDays($package->duration);
            } else {
                // TH còn hạn
                $expiredDate = now()->addDays($package->duration);
            }
            $rent_package->update(['expired_at' => $expiredDate, 'status' => 1]);
            $order = Order::create([
                'rent_package_id' => $rent_package->id,
                'amount' => $package->price,
                'day' => $package->duration,
                'type' => 1,
                'status' => 1,
            ]);
            $currentDateTime = Carbon::now();
            $typeAbbreviation = strtoupper(substr($package->type, 0, 3));
            $invoiceCode = $typeAbbreviation.$currentDateTime->format('YmdHis');
            Invoice::create([
                'order_id' => $order->id,
                'payment_gateway' => 1,
                'code' => $invoiceCode,
            ]);

            $user->balance -= $package->price;
            $user->save();

            $this->balance->logBalance(Auth::id(), $package->price, 'minus');

            return response()->json(['status' => true, 'title' => 'Thông báo', 'message' => 'Mua thành công']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'title' => 'Lỗi', 'message' => 'Đã xảy ra lỗi khi mua']);
        }
    }
}
