<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RentPackageProxy extends Model
{
    use HasFactory;

    protected $table = 'rent_package_proxy';

    public $timestamps = false;

    /**
     * @var array
     */
    protected $fillable = [
        'rent_package_id',
        'proxy_id',
    ];

    /**
     * @var array
     */
    protected $casts = [
        'rent_package_id' => 'integer',
        'proxy_id' => 'integer',
    ];
}
