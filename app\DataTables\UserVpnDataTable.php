<?php

namespace App\DataTables;

use App\Models\RentPackage;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class UserVpnDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->addColumn('action', 'user.vpn.table_action')
            ->editColumn('price', function ($query) {
                return number_format($query->price);
            })
            ->rawColumns(['action']);
    }

    public function query(): Builder
    {
        $query = RentPackage::query()
            ->select('rent_packages.*', 'packages.name as packages.name', 'packages.duration as packages.duration', 'packages.price as packages.price')
            ->leftJoin('packages', 'packages.id', '=', 'rent_packages.package_id')
            ->whereHas('user', function ($query) {
                $query->where('id', '=', Auth::id());
            })
            ->where('packages.type', '=', 'vpn')
            ->where('rent_packages.status', '=', 1);

        return $query;
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
//            ->minifiedAjax('/vpn', $this->getScript(), [], ['error' => 'function (err) { defaultOnError(err);}'])
            ->addAction(['width' => '80px', 'responsivePriority' => -1, 'printable' => false, 'title' => __('Chức năng'), 'visible' => Auth::user()->type === User::USER ? true : false])
            ->parameters([
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'packages.name' => (new Column([
                'title' => __('Tên gói'),
                'data' => 'packages.name',
                'searchable' => true,
                'orderable' => false,
            ])),
            'packages.price' => (new Column([
                'title' => __('Giá'),
                'data' => 'packages.price',
                'searchable' => true,
                'orderable' => false,
            ])),
            'packages.duration' => (new Column([
                'title' => __('Số ngày'),
                'data' => 'packages.duration',
                'searchable' => true,
                'orderable' => false,
            ])),
            'created_at' => (new Column([
                'title' => __('Ngày mua'),
                'data' => 'created_at',
                'searchable' => true,
                'orderable' => false,
            ])),
            'expired_at' => (new Column([
                'title' => __('Ngày hết hạn'),
                'data' => 'expired_at',
                'searchable' => true,
                'orderable' => false,
            ])),
        ];
    }

    protected function filename(): string
    {
        return __('Danh sách vpn').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
