<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserProxy extends Model
{
    use HasFactory;

    protected $table = 'user_proxies';

    public $timestamps = false;

    /**
     * @var array
     */
    protected $fillable = [
        'proxy_id',
        'user_id',
    ];

    /**
     * @var array
     */
    protected $casts = [
        'proxy_id' => 'integer',
        'user_id' => 'integer',
    ];
}
