<?php

namespace App\Models;

use App\Casts\DateTimeCast;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Invoice extends Model
{
    use HasFactory;

    protected $table = 'invoices';

    /**
     * @var array
     */
    protected $fillable = [
        'id',
        'order_id',
        'payment_gateway',
        'code',
        'created_at' => DateTimeCast::class,
        'updated_at' => DateTimeCast::class,
    ];

    /**
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'order_id' => 'integer',
        'payment_gateway' => 'string',
        'code' => 'string',
        'created_at' => DateTimeCast::class,
        'updated_at' => DateTimeCast::class,
    ];

    public function order(): HasOne
    {
        return $this->hasOne(Order::class, 'id', 'order_id');
    }
}
