<form class="form" id="kt_login_register_form" novalidate>
    @csrf
    <div class="pb-8 text-center">
        <h2 class="font-size-h1-lg font-size-h2 font-weight-bolder text-dark">{{ __('Đăng ký tài khoản') }}</h2>
        <p class="font-size-h4 font-weight-bold text-muted">{{ __('Điền đầy đủ các thông tin') }}
    </div>
    <div class="form-group">
        <label for="register_name">{{ __('Họ và tên') }}:</label>
        <input id="register_name" class="form-control form-control-solid" type="text"
               placeholder="{{ __('Họ và tên') }}"
               name="name"/>
    </div>
    <div class="form-group">
        <label for="register_email">{{ __('Email') }}:</label>
        <input id="register_email" class="form-control form-control-solid" type="text"
               placeholder="{{ __('Email') }}"
               name="email"/>
    </div>
    <div class="form-group">
        <label for="register_phone">{{ __('Số điện thoại') }}:</label>
        <input id="register_phone" class="form-control form-control-solid" type="text"
               placeholder="{{ __('Số điện thoại') }}"
               name="phone"/>
    </div>
    <div class="form-group">
        <label for="register_password">{{ __('Mật khẩu') }}:</label>
        <input id="register_password" class="form-control form-control-solid" type="password"
               placeholder="{{ __('Mật khẩu') }}"
               name="password"/>
    </div>
    <div class="form-group">
        <label for="register_password_confirm">{{ __('Nhập lại mật khẩu') }}:</label>
        <input id="register_password_confirm" class="form-control form-control-solid" type="password"
               placeholder="{{ __('Nhập lại mật khẩu') }}"
               name="password_confirm"/>
    </div>
    <a href="#" id="show_invite_code"> <label for="register_invite_code">{{ __('Mã giới thiệu') }}:</label></a>
    <div class="form-group" id="invite_code_section" style="display: none;">
        <input id="register_invite_code" class="form-control form-control-solid" type="text" value="{{ session('invite_code') }}"
               placeholder="{{ __('Nhập mã giới thiệu') }}"
               name="invite_code"/>
    </div>
    <div class="d-flex flex-center flex-wrap form-group pb-3 pb-lg-0">
        <button class="btn btn-primary font-size-h6 font-weight-bolder mx-4 my-3 px-8 py-4" id=kt_login_register_submit type="button">{{ __('Đăng ký') }}</button>
        <button class="btn btn-light-primary font-size-h6 font-weight-bolder mx-4 my-3 px-8 py-4" id=kt_login_register_cancel type="button">{{ __('Thoát') }}</button>
    </div>
</form>
@push('scripts')
    <script type="text/javascript">
        var validation_register;
        validation_register = FormValidation.formValidation(
            KTUtil.getById('kt_login_register_form'), {
                fields: {
                    token_reset: {
                        validators: {
                            notEmpty: {
                                message: '{{ __('Vui lòng điền mã xác nhận') }}'
                            }
                        }
                    },
                    phone: {
                        validators: {
                            notEmpty: {
                                message: '{{ __('Vui lòng điền mật khẩu') }}'
                            },
                        }
                    },
                    password: {
                        validators: {
                            notEmpty: {
                                message: '{{ __('Vui lòng điền mật khẩu') }}'
                            },
                            checkPassword: {
                                message: '{{ __('Vui lòng nhập mật khẩu mạnh hơn (bao gồm cả chữ thường và chữ hoa và số)') }}'
                            },
                        }
                    },
                    password_confirm: {
                        validators: {
                            notEmpty: {
                                message: '{{ __('Vui lòng nhập lại mật khẩu') }}'
                            },
                            identical: {
                                compare: function() {
                                    return form.querySelector('[name="register_password"]').value;
                                },
                                message: '{{ __('Nhập lại mật khẩu không trùng khớp') }}'
                            }
                        }
                    },
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap()
                }
            }
        );
        $('#kt_login_register_submit').on('click', function(e) {
            e.preventDefault();
            var csrfToken = $('#kt_login_register_form input[name="_token"]').val();

            validation_register.validate().then(function(status) {
                if (status !== 'Valid') {
                    mess_trial()
                } else {
                    axios.post('register', {
                        _token: csrfToken,
                        name: $("#register_name").val(),
                        email: $("#register_email").val(),
                        phone: $("#register_phone").val(),
                        password: $("#register_password").val(),
                        invite_code: $("#register_invite_code").val(),
                    }).then(function(response) {
                        if (response.data.status) {
                            mess_success(response.data.title,response.data.message)
                            setTimeout(function() {
                                window.location.href = '/login';
                            }, 2000);
                            // _showForm('login');
                        } else {
                            mess_error(response.data.title,response.data.message)
                        }
                    });
                }
            });
        });
        // $('#kt_login_register_cancel').on('click', function(e) {
        //     e.preventDefault();
        //     _showForm('signin');
        // });
        $('#kt_login_register_cancel').on('click', function(e) {
            e.preventDefault();
            _showForm('signin');
        });
    </script>
    <script>
        document.getElementById('show_invite_code').addEventListener('click', function(event) {
            event.preventDefault();

            var inviteCodeSection = document.getElementById('invite_code_section');
            if (inviteCodeSection.style.display === 'none') {
                inviteCodeSection.style.display = 'block';
            } else {
                inviteCodeSection.style.display = 'none';
            }
        });
    </script>
@endpush
