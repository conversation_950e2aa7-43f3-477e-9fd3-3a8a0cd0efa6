<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\Package;
use App\Models\Proxy;
use App\Models\RentPackage;
use App\Models\RentPackageProxy;
use App\Models\User;
use App\Models\UserProxy;
use App\Services\BalanceService;
use App\Services\PackageService;
use App\Services\ProxyService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class ShopController extends Controller
{
    private ProxyService $proxyService;

    private BalanceService $balance;

    private PackageService $packageService;

    public function __construct(
        ProxyService $proxyService,
        BalanceService $balanceService,
        PackageService $packageService
    ) {
        $this->proxyService = $proxyService;
        $this->balance = $balanceService;
        $this->packageService = $packageService;
    }

    public function index()
    {
        $proxyPackages = Package::where('type', 'proxy')->get();
        $vpnPackages = Package::where('type', 'vpn')->get();

        return view('user.shop.index', ['proxyPackages' => $proxyPackages, 'vpnPackages' => $vpnPackages]);
    }

    public function buyPackage($id)
    {
        $user_id = Auth::id();

        try {
            $package = Package::findOrFail($id);
            $user = User::findOrFail($user_id);
            if ($user->balance < $package->price) {
                return response()->json(['status' => false, 'title' => 'Lỗi', 'message' => 'Xin lỗi, tài khoản không đủ tiền']);
            }
            $rentProxies = Proxy::query()->disableCache()
                ->whereDoesntHave('user')
                ->limit($package->quantity)
                ->get();

            $availableProxyCount = $rentProxies->count();
            if (($package->type == 'proxy' && $availableProxyCount >= $package->quantity) || ($package->type == 'vpn')) {
                $api_key = $this->packageService->get_api_key();
                $expiredDate = now()->addDays($package->duration);
                $rentPackage = RentPackage::create([
                    'user_id' => $user_id,
                    'package_id' => $id,
                    'expired_at' => $expiredDate,
                    'api_key' => $api_key,
                    'status' => 1,
                ]);
                $order = Order::create([
                    'rent_package_id' => $rentPackage->id,
                    'amount' => $package->price,
                    'day' => $package->duration,
                    'type' => 1,
                    'status' => 1,
                ]);
                if ($package->type == 'proxy') {
                    foreach ($rentProxies as $proxy) {
                        RentPackageProxy::create([
                            'rent_package_id' => $rentPackage->id,
                            'proxy_id' => $proxy->id,
                        ]);
                        UserProxy::create([
                            'user_id' => $user_id,
                            'proxy_id' => $proxy->id,
                        ]);
                        $this->proxyService->reset($proxy->id);
                    }
                }
                $currentDateTime = Carbon::now();
                $typeAbbreviation = strtoupper(substr($package->type, 0, 3));
                $invoiceCode = $typeAbbreviation.$currentDateTime->format('YmdHis');
                Invoice::create([
                    'order_id' => $order->id,
                    'payment_gateway' => 1,
                    'code' => $invoiceCode,
                ]);

                $user->balance -= $package->price;
                $user->save();

                $this->balance->logBalance(Auth::id(), $package->price, 'minus');

                return response()->json(['status' => true, 'title' => 'Thông báo', 'message' => 'Mua thành công']);
            } else {
                return response()->json(['status' => false, 'title' => 'Lỗi', 'message' => 'Xin lỗi, cửa hàng không đủ tài nguyên']);
            }

        } catch (\Exception $e) {
            return response()->json(['status' => false, 'title' => 'Lỗi', 'message' => 'Đã xảy ra lỗi khi mua']);
        }
    }
}
