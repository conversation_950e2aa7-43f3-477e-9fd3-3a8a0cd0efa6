<?php

namespace App\Repositories;

use App\Models\DepositHistory;
use Illuminate\Database\Eloquent\Builder;

class DepositHistoryRepository extends BaseRepository
{
    public function model(): string
    {
        return DepositHistory::class;
    }

    public function datatable(): Builder
    {
        return $this->query()->with(['user' => function ($query) {
            $query->withTrashed();
        }]);
    }
}
