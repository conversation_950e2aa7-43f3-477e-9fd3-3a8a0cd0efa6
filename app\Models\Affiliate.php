<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Affiliate extends Model
{
    use HasFactory;

    protected $table = 'affiliate';

    public $timestamps = false;

    /**
     * @var array
     */
    protected $fillable = [
        'user_id', // người được giới thiệu
        'referrer_id', // người giới thiệu
    ];

    /**
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'referrer_id' => 'integer',
    ];

    public function user(): belongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
