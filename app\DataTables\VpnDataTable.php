<?php

namespace App\DataTables;

use App\Repositories\VpnRepository;
use Illuminate\Database\Eloquent\Builder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class VpnDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->addColumn('action', 'admin.vpn.table_action')
            ->editColumn('type', function ($query) {
                if ($query->type == 0) {
                    return 'Free';
                }
                if ($query->type == 1) {
                    return 'Vip';
                }
            });
    }

    public function query(): Builder
    {
        return (new VpnRepository())->vpn_datatable();
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax('/admin/vpn', $this->getScript(), [], ['error' => 'function (err) { defaultOnError(err);}'])
            ->addAction(['width' => '80px', 'responsivePriority' => -1, 'printable' => false, 'title' => __('Chức năng')])
            ->parameters([
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'ip_address' => (new Column([
                'title' => __('Địa chỉ IP'),
                'data' => 'ip_address',
                'searchable' => true,
                'orderable' => false,
            ])),
            'type' => (new Column([
                'title' => __('Loại'),
                'data' => 'type',
                'searchable' => true,
                'orderable' => false,
            ])),
            'country' => (new Column([
                'title' => __('Quốc gia'),
                'data' => 'country',
                'searchable' => true,
                'orderable' => false,
            ])),
            'state' => (new Column([
                'title' => __('Tỉnh/Thành'),
                'data' => 'state',
                'searchable' => true,
                'orderable' => false,
            ])),
            'city' => (new Column([
                'title' => __('Quận/Huyện'),
                'data' => 'city',
                'searchable' => true,
                'orderable' => false,
            ])),
            'zipcode' => (new Column([
                'title' => __('Zipcode'),
                'data' => 'zipcode',
                'searchable' => true,
                'orderable' => false,
            ])),
            'isp' => (new Column([
                'title' => __('Isp'),
                'data' => 'isp',
                'searchable' => true,
                'orderable' => false,
            ])),
        ];
    }

    protected function filename(): string
    {
        return __('Danh sách vpn').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
