<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\MyProfileRequest;
use App\Repositories\UserRepository;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class MyProfileController extends Controller
{
    private UserRepository $user;

    public function __construct(UserRepository $userRepository)
    {
        $this->user = $userRepository;
    }

    public function index(): View
    {
        if (Auth::user()->type == 0) {
            return view('auth.admin_profile');
        } elseif (Auth::user()->type == 1) {
            return view('auth.user_profile');
        }
    }

    public function update(MyProfileRequest $request): JsonResponse
    {
        $input = $request->all();
        try {
            if (array_key_exists('avatar', $input)) {
                $response = $this->move_file(Auth::user()->avatar, Auth::id(), storage_path('app/public/images/avatars/'), $input['avatar']);
                $response = $this->json2array($response)['original'];
                if (! $response['status']) {
                    return $this->error($response['message']);
                }
                $input['avatar'] = $response['message'];
            }
            $this->user->update(Auth::id(), $input);
        } catch (\Throwable $e) {
        }

        return $this->question('Bạn có muốn tải lại trang để hiển thị dữ liệu mới không?');
    }
}
