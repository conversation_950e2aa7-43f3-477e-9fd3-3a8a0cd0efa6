version: "3"

services:
  webserver:
    build:
      context: .
      dockerfile: ./docker/Dockerfile
    container_name: nginx_php
    platform: linux/arm64
    working_dir: /var/www/html
    ports:
      - "8000:8000"
    volumes:
      - .:/var/www/html
      - ./docker/logs/nginx:/var/log/nginx
      - ./docker/logs/php-fpm/php-fpm.log:/var/log/php-fpm.log
      - ./docker/config/app.conf:/etc/nginx/conf.d/app.conf
      - ./docker/config/custom.ini:/usr/local/etc/php/conf.d/custom.ini
    links:
      - database

  database:
    image: mysql:8.0
    platform: linux/arm64
    container_name: mysql
    ports:
      - "3308:3306"
    volumes:
      - ./docker/data/mysql:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
