<?php

namespace App\DataTables;

use App\Models\Affiliate;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AffiliateDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->editColumn('users_balance', function ($query) {
                return number_format($query->users_balance);
            });
    }

    public function query(): Builder
    {
        $query = Affiliate::query()
            ->join('users', 'users.id', '=', 'affiliate.user_id')
            ->select('users.name as users_name', 'users.created_at as users_created_at', 'users.email as users_email', 'users.balance as users_balance')
            ->where('affiliate.referrer_id', '=', Auth::id());

        return $query;
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax('/affiliate', $this->getScript(), [], ['error' => 'function (err) { defaultOnError(err);}'])
//            ->addAction(['width' => '80px', 'responsivePriority' => -1, 'printable' => false, 'title' => __('Chức năng')])
            ->parameters([
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'users_name' => (new Column([
                'title' => __('Người dùng'),
                'data' => 'users_name',
                'searchable' => true,
                'orderable' => false,
            ])),
            'users_email' => (new Column([
                'title' => __('Email'),
                'data' => 'users_email',
                'searchable' => true,
                'orderable' => false,
            ])),
            'users_balance' => (new Column([
                'title' => __('Số tiền đã nạp'),
                'data' => 'users_balance',
                'searchable' => true,
                'orderable' => false,
            ])),
            'users_created_at' => (new Column([
                'title' => __('Thời gian đăng ký'),
                'data' => 'users_created_at',
                'searchable' => true,
                'orderable' => false,
            ])),
        ];
    }

    protected function filename(): string
    {
        return __('Danh sách tiếp thị').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
