<div class="form-group">
    <label for="{{ $type }}ip_address">{{ __('Địa chỉ IP') }}<span style="color: red">*</span>:</label>
    <input id="{{ $type }}ip_address" name="ip_address" type="text" class="form-control form-control-solid"
           placeholder="{{ __('Địa chỉ IP') }}" />
</div>
<div class="form-group">
    <label for="{{ $type }}name">{{ __('Tên') }}<span style="color: red">*</span>:</label>
    <input id="{{ $type }}name" name="name" type="text" class="form-control form-control-solid"
           placeholder="{{ __('Tên') }}" />
</div>
<div class="form-group">
    <label for="{{ $type }}type">{{ __('Loại') }}<span style="color: red">*</span>:</label>
    <select class="form-control form-control-solid" name="type" id="{{ $type }}type">
        <option value="0">Free</option>
        <option value="1">Vip</option>
    </select>
</div>
<div class="form-group">
    <label for="{{ $type }}username">{{ __('Tài khoản') }}:</label>
    <input id="{{ $type }}username" name="username" type="text" class="form-control form-control-solid"
           placeholder="{{ __('Tài khoản') }}" />
</div>
<div class="form-group">
    <label for="{{ $type }}password">{{ __('Mật khẩu') }}:</label>
    <input id="{{ $type }}password" name="password" type="text" class="form-control form-control-solid"
           placeholder="{{ __('Mật khẩu') }}" />
</div>
<div class="form-group">
    <label for="{{ $type }}config_udp">{{ __('OVPN Config Script [UDP]') }}:</label>
    <textarea id="{{ $type }}config_udp" name="config_udp" type="text" class="form-control form-control-solid"
           placeholder="{{ __('OVPN Config Script [UDP]') }}"></textarea>
</div>
<div class="form-group">
    <label for="{{ $type }}config_tcp">{{ __('OVPN Config Script [TCP]') }}:</label>
    <textarea id="{{ $type }}config_tcp" name="config_tcp" type="text" class="form-control form-control-solid"
           placeholder="{{ __('OVPN Config Script [TCP]') }}"></textarea>
</div>
