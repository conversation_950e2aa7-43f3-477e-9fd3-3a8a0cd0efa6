.login.login-1 .login-aside .aside-img {
    min-height: 450px
}

.login.login-1 .login-forgot, .login.login-1 .login-signin, .login.login-1 .login-reset .login-register {
    display: none
}

.login.login-1.login-signin-on .login-reset .login-register {
    display: none
}

.login.login-1.login-signin-on .login-signin {
    display: block
}

.login.login-1.login-signin-on .login-forgot .login-register {
    display: none
}

.login.login-1.login-reset-on .login-reset {
    display: block
}

.login.login-1.login-reset-on .login-signin .login-register {
    display: none
}

.login.login-1.login-reset-on .login-forgot .login-register{
    display: none
}

.login.login-1.login-forgot-on .login-reset .login-register{
    display: none
}

.login.login-1.login-forgot-on .login-signin .login-register{
    display: none
}

.login.login-1.login-forgot-on .login-forgot {
    display: block
}

.login.login-1.login-register-on .login-signin .login-reset login-signin{
    display: none
}

.login.login-1.login-register-on .login-register {
    display: block
}

@media (min-width: 992px) {
    .login.login-1 .login-aside {
        width: 100%;
        max-width: 700px
    }

    .login.login-1 .login-content {
        width: 100%;
        max-width: 500px
    }

    .login.login-1 .login-content .login-form {
        width: 100%;
        max-width: 450px
    }
}

@media (min-width: 992px) and (max-width: 1399.98px) {
    .login.login-1 .login-aside {
        width: 100%;
        max-width: 450px
    }
}

@media (max-width: 991.98px) {
    .login.login-1 .login-content .login-form {
        width: 100%;
        max-width: 400px
    }
}

@media (max-width: 575.98px) {
    .login.login-1 .aside-img {
        min-height: 300px !important;
        background-size: 400px
    }

    .login.login-1 .login-content .login-form {
        width: 100%;
        max-width: 100%
    }
}
