<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (! Schema::hasTable('orders')) {
            Schema::create('orders', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('rent_package_id');
                $table->double('amount');
                $table->integer('day');
                $table->integer('type');
                $table->integer('status')->default(0);
                $table->timestamps();
            });
        }
        Schema::table('orders', function (Blueprint $table) {
            $table->foreign('rent_package_id')->references('id')->on('rent_packages')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
