<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'account_id',
        'code',
        'amount',
        'type', //minus or plus
        'from_name',
        'from_account',
        'from_bank',
        'note',
        'received_at',
    ];
}
