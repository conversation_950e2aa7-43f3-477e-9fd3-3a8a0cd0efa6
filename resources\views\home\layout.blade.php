<!DOCTYPE html>
<html class="no-js" lang="en">

<head>
    <title>PROXYTN</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
    <meta name="viewport" content="user-scalable=no, width=device-width, height=device-height, initial-scale=1, maximum-scale=1, minimum-scale=1, minimal-ui" />

    <meta name="theme-color" content="#056EB9" />
    <meta name="msapplication-navbutton-color" content="#056EB9" />
    <meta name="apple-mobile-web-app-status-bar-style" content="#056EB9" />

    <!-- Favicons
    ================================================== -->
    <link rel="shortcut icon" href="images/favicon.ico">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="images/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="114x114" href="images/apple-touch-icon-114x114.png">

    <!-- Critical styles
    ================================================== -->
    <link rel="stylesheet" href="{{ asset('assets/css/index/critical.min.css') }}" type="text/css">

    <!-- Load google font
    ================================================== -->
    <script type="text/javascript">
        WebFontConfig = {
            google: { families: [ 'Nunito+Sans:400,400i,700,700i,800,800i,900,900i', 'Quicksand:300,400,700'] }
        };
        (function() {
            var wf = document.createElement('script');
            wf.src = ('https:' == document.location.protocol ? 'https' : 'http') + '://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js';
            wf.type = 'text/javascript';
            wf.async = 'true';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(wf, s);
        })();
    </script>

    <!-- Load other scripts
    ================================================== -->
    <script type="text/javascript">
        var _html = document.documentElement,
            isTouch = (('ontouchstart' in _html) || (navigator.msMaxTouchPoints > 0) || (navigator.maxTouchPoints));

        _html.className = _html.className.replace("no-js","js");
        _html.classList.add( isTouch ? "touch" : "no-touch");
    </script>
    <script type="text/javascript" src="{{ asset('assets/js/device.min.js') }}"></script>
    <style>
        .top-bar__navigation ul {
            display: flex;
            justify-content: center;
            align-items: center;
            list-style: none;
            padding: 0;
            margin-left: auto;
            margin-right: auto;
        }

        .top-bar__navigation ul li {
            margin: 0 10px;
        }
    </style>
</head>

<body>
<div id="app">
    <!-- start header -->
    <header id="top-bar" class="top-bar top-bar--dark" data-nav-anchor="true">
        <div class="top-bar__inner">
            <div class="container-fluid">
                <div class="row align-items-center no-gutters">
                    <a class="top-bar__logo site-logo" href="#">
                        <img class="img-fluid" src="images/site_logo/logo_2.png" width="159" height="45" alt="demo" />
                    </a>
                    <a id="top-bar__navigation-toggler" class="top-bar__navigation-toggler" href="javascript:void(0);">
                        <span></span>
                    </a>
                    <div class="top-bar__collapse">
                        <nav id="top-bar__navigation" class="top-bar__navigation" style="margin-left: 400px;" role="navigation">
                            <ul>
                                <li>
                                    <a class="nav-link active" href="#start-screen">Trang chủ</a>
                                </li>

                                <li>
                                    <a class="nav-link" href="#pricing">Sản phẩm</a>
                                </li>
                                <li>
                                    <a class="nav-link" href="#pricing">Điều khoản</a>
                                </li>
                                <li>
                                    <a class="nav-link" href="#pricing">Chính sách</a>
                                </li>
                            </ul>
                        </nav>
                        <div id="top-bar__action" class="top-bar__action">
                            <div class="d-xl-flex flex-xl-row flex-xl-wrap align-items-xl-center">


                                <div class="top-bar__auth-btns">
                                    @if(Auth::id() == 1)
                                        <a class="custom-btn custom-btn--medium custom-btn--style-3" href="{{ route('admin.balance_history.index') }}">Bảng tổng quan</a>
                                    @elseif(Auth::id() > 1)
                                        <a class="custom-btn custom-btn--medium custom-btn--style-3" href="{{ route('balance_history.index') }}">Bảng tổng quan</a>
                                    @else
                                        <a class="custom-btn custom-btn--medium custom-btn--style-3" href="{{ route('login') }}">Đăng nhập</a>
                                        <a class="custom-btn custom-btn--medium custom-btn--style-3 ml-1" href="{{ route('register') }}">Đăng ký</a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </header>

    <main role="main">
        <!-- Common  styles
        ================================================== -->
        <link rel="stylesheet" href="{{ asset('assets/css/index/style.min.css') }}" type="text/css">

        <!-- Load lazyLoad scripts
        ================================================== -->
        <script>
            (function(w, d){
                var m = d.getElementsByTagName('main')[0],
                    s = d.createElement("script"),
                    v = !("IntersectionObserver" in w) ? "8.17.0" : "11.0.6",
                    o = {
                        elements_selector: ".lazy",
                        threshold: 500,
                        callback_enter: function (element) {

                        },
                        callback_load: function (element) {

                        },
                        callback_set: function (element) {

                            oTimeout = setTimeout(function ()
                            {
                                clearTimeout(oTimeout);

                                AOS.refresh();
                            }, 1000);
                        },
                        callback_error: function(element) {
                            element.src = "https://placeholdit.imgix.net/~text?txtsize=21&txt=Image%20not%20load&w=200&h=200";
                        }
                    };
                s.type = 'text/javascript';
                s.async = false; // This includes the script as async. See the "recipes" section for more information about async loading of LazyLoad.
                s.src = "https://cdn.jsdelivr.net/npm/vanilla-lazyload@" + v + "/dist/lazyload.min.js";
                m.appendChild(s);
                // m.insertBefore(s, m.firstChild);
                w.lazyLoadOptions = o;
            }(window, document));
        </script>

        <!-- start section -->
        <section class="section">
            <div class="container">
                <div class="row">
                    <div class="col-12">

                        <div class="content-container">
                            <h1>Heading 1 2<span>70px</span></h1>
                            <h2>Heading 2 <span>40px</span></h2>
                            <h3>Heading 3 <span>30px</span></h3>
                            <h4>Heading 4 <span>20px</span></h4>
                            <h5>Heading 5 <span>16px</span></h5>
                            <h6>Heading 6 <span>13px</span></h6>

                            <hr>

                            <h4>Paragraphs</h4>

                            <p>
                                <strong>Earthworm eel sole ropefish armorhead sole Black scabbardfish. Codling goldfish golden dojo combtail gourami tench deep sea anglerfish, plownose chimaera. Jackson shark! Yellow-eye mullet worm eel catalufa weever, damselfish, worm eel pricklefish dorado</strong>
                            </p>

                            <p>
                                Redfin <span class="demo-selection">perch tripod fish zebra lionfish</span>, nase slickhead! Jewelfish angler Devario gray reef shark forehead brooder. Pike Redfin perch tripod fish zebra lionfish, nase slickhead! Jewelfish angler <a href="#">Devario gray reef shark forehead brooder</a>.
                            </p>

                            <p>
                                <em>False cat shark, sand dab Siamese fighting fish. African glass catfish inconnu Celebes rainbowfish North American freshwater catfish drum algae eater murray cod thorny catfish knifejaw prickly shark. Icefish; Atlantic trout whitefish deep sea anglerfish threadfin eeltail catfish riffle dace pikeblenny Moses sole: cobia spikefish sucker viperfish, alligatorfish tapetail mudskipper, yellowfin surgeonfish? Earthworm eel sole ropefish armorhead sole Black scabbardfish. Codling goldfish golden dojo combtail gourami tench deep sea anglerfish, plownose chimaera.</em>
                            </p>

                            <hr>

                            <h4>Dropcaps</h4>

                            <p class="dropcaps">
                                <span class="first-letter">L</span> We believe in helping brands create through strategy, and integrated experiences on web, mobile, and in the world. And you're here, friends, because you also believe.Our team has a passion for making things with real value. This has led us to assemble a multi-talented group that can do just about anything: from building sets to photographing food, crafting websites to developing apps, beautiful design to adventure cinematography. Designers, engineers, creatives, makers, developers, artists, unite. Let’s do something real-special together.
                            </p>

                            <hr>

                            <h4>Standard Listings</h4>

                            <div class="row">
                                <div class="col-12 col-md-6 col-lg-4">
                                    <div class="">
                                        <ul>
                                            <li>Minnow arrowtooth</li>
                                            <li>Southern sandfish</li>
                                            <li>Black triplefin guitarfish</li>
                                            <li>Nase slickhead</li>
                                            <li>Reef shark <a href="#">forehead brooder</a></li>
                                            <li>Minnow arrowtooth</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="spacer py-3 d-md-none"></div>

                                <div class="col-12 col-md-6 col-lg-4">
                                    <div class="">
                                        <ol>
                                            <li>Minnow arrowtooth</li>
                                            <li>Southern sandfish</li>
                                            <li>Black triplefin guitarfish</li>
                                            <li>Nase slickhead</li>
                                            <li>Reef shark <a href="#">forehead brooder</a></li>
                                            <li>Minnow arrowtooth</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <h4>Blockquotes</h4>

                            <blockquote class="blockquot">
                                <p>
                                    <strong>This has led us to assemble a multi-talented group that can do just about anything: from building sets to photographing food, crafting websites to developing apps, beautiful design to adventure cinematography. Designers, engineers, creatives,</strong>
                                </p>
                            </blockquote>
                        </div>

                    </div>
                </div>
            </div>
        </section>
        <!-- end section -->
    </main>

    <!-- start footer -->
    <footer class="footer footer--s1 footer--color-light">
        <div class="footer__line footer__line--first">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-md-4 col-lg-4 col-xl-3">
                        <div class="footer__item">
                            <a class="footer__logo site-logo" href="#">
                                <img class="img-fluid lazy" src="images/blank.gif" data-src="images/site_logo/logo_3.png" width="159" height="45" alt="demo" />
                            </a>
                        </div>

                        <div class="footer__item">
                            <span class="__copy">Copyrights 2024 © <a class="__dev" href="https://proxytn.com" target="_blank">ProxyTN</a></span>
                        </div>
                    </div>

                    <div class="col-12 col-md-5 col-lg-3 offset-xl-1">
                        <div class="footer__item">
                            <address class="footer__address footer__address--s1">
                                <a href="mailto:<EMAIL>"><EMAIL></a> <br>
                            </address>
                        </div>
                    </div>



                    <div class="col-12 col-lg-3">
                        <div class="footer__item">
                            <!-- start social buttons -->
                            <div class="s-btns s-btns--md s-btns--colored s-btns--rounded">
                                <ul class="d-flex flex-row flex-wrap align-items-center">
                                    <li><a class="f" href="https://facebook.com/"><i class="fontello-facebook"></i></a></li>
                                    <li><a class="t" href="https://x.com/"><i class="fontello-twitter"></i></a></li>
                                    <li><a class="y" href="https://youtube.com/"><i class="fontello-youtube-play"></i></a></li>
                                </ul>
                            </div>
                            <!-- end social buttons -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer__waves-container">
            <svg class="footer__wave js-wave" data-wave='{"height": 40, "bones": 6, "amplitude": 70, "color": "rgba(78, 111, 136, 0.14)", "speed": 0.3}' width="100%" height="100%" version="1.1" xmlns="http://www.w3.org/2000/svg"><defs></defs><path d=""/></svg>

            <svg class="footer__wave js-wave" data-wave='{"height": 60, "bones": 5, "amplitude": 90, "color": "rgba(243, 248, 249, 0.02)", "speed": 0.35}' width="100%" height="100%" version="1.1" xmlns="http://www.w3.org/2000/svg"><defs></defs><path d=""/></svg>
        </div>
    </footer>
    <!-- end footer -->
</div>

<div id="btn-to-top-wrap">
    <a id="btn-to-top" class="circled" href="javascript:void(0);" data-visible-offset="800"></a>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js"></script>
<script>window.jQuery || document.write('<script src="{{ asset('assets/js/jquery-2.2.4.min.js')  }}"><\/script>')</script>

<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>

<script type="text/javascript" src="{{ asset('assets/js/main.min.js') }}"></script>

</body>
</html>
