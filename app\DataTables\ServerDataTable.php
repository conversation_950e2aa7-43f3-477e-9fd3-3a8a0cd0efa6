<?php

namespace App\DataTables;

use App\Repositories\ServerRepository;
use Illuminate\Database\Eloquent\Builder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class ServerDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->addColumn('action', 'admin.server.table_action');
    }

    public function query(): Builder
    {
        return (new ServerRepository())->datatable();
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax('/admin/servers', $this->getScript(), [], ['error' => 'function (err) { defaultOnError(err);}'])
            ->addAction(['width' => '80px', 'responsivePriority' => -1, 'printable' => false, 'title' => __('Chức năng')])
            ->parameters([
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'ip_address' => (new Column([
                'title' => __('Địa chỉ IP'),
                'data' => 'ip_address',
                'searchable' => true,
                'orderable' => false,
            ])),
            'state' => (new Column([
                'title' => __('Tỉnh/Thành'),
                'data' => 'state',
                'searchable' => true,
                'orderable' => false,
            ])),
            'city' => (new Column([
                'title' => __('Quận/Huyện'),
                'data' => 'city',
                'searchable' => true,
                'orderable' => false,
            ])),
            'proxies_count' => (new Column([
                'title' => __('Số lượng proxy'),
                'data' => 'proxies_count',
                'searchable' => true,
                'orderable' => false,
            ])),
        ];
    }

    protected function filename(): string
    {
        return __('Danh sách máy chủ').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
