<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Update\UpdateServerRequest;
use App\Repositories\ServerRepository;

class ServerController extends Controller
{
    private ServerRepository $serverRepository;

    public function __construct(ServerRepository $serverRepository)
    {
        $this->serverRepository = $serverRepository;
    }

    public function patch(int $id, UpdateServerRequest $request): \Illuminate\Http\JsonResponse
    {
        $input = $request->only('ip_address');
        $query = $this->serverRepository->update($id, $input);
        if ($query) {
            return $this->success();
        }

        return $this->error();
    }
}
