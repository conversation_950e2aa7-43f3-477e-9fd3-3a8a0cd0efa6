<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_package',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatablePackage;
        $(document).ready(function() {
            DatatablePackage = window.LaravelDataTables["datatable_package"];
        });
        $(document).on('click', '.buy_package', function (e) {
            e.preventDefault();
            let id = $(this).data('id');

            Swal.fire({
                title: '<PERSON><PERSON><PERSON> nhận',
                text: 'Bạn có chắc chắn muốn mua gói proxy này không?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Có',
                cancelButtonText: 'Không',
            }).then((result) => {
                if (result.isConfirmed) {
                    axios.post('shop/buy/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Thành công',
                                    text: '<PERSON><PERSON> gó<PERSON> thành công!',
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                mess_error(response.data.title, response.data.message);
                            }
                        });
                }
            });
        });

    </script>
@endpush

