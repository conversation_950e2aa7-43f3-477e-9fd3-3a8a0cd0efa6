{"version": 3, "sources": ["device.js"], "names": ["device", "previousDevice", "addClass", "documentElement", "find", "handleOrientation", "hasClass", "orientationEvent", "removeClass", "userAgent", "window", "document", "navigator", "toLowerCase", "ios", "iphone", "ipod", "ipad", "windows", "android", "androidPhone", "androidTablet", "blackberry", "blackberryPhone", "blackberryTablet", "windowsPhone", "windowsTablet", "fxos", "fxosPhone", "fxosTablet", "meego", "<PERSON><PERSON>", "location", "protocol", "nodeWebkit", "process", "mobile", "tablet", "desktop", "television", "i", "length", "portrait", "innerHeight", "innerWidth", "landscape", "noConflict", "this", "needle", "indexOf", "className", "regex", "RegExp", "match", "currentClassNames", "replace", "Object", "prototype", "hasOwnProperty", "call", "addEventListener", "attachEvent", "define", "amd", "module", "exports"], "mappings": "CAMA,WAEA,IAAAA,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAGAR,EAAAS,OAAAV,OAEAA,EAAA,GAGAU,OAAAV,OAAAA,EAGAG,EAAAO,OAAAC,SAAAR,gBAIAM,EAAAC,OAAAE,UAAAH,UAAAI,cAKAb,EAAAc,IAAA,WACA,OAAAd,EAAAe,UAAAf,EAAAgB,QAAAhB,EAAAiB,QAGAjB,EAAAe,OAAA,WACA,OAAAf,EAAAkB,WAAAd,EAAA,WAGAJ,EAAAgB,KAAA,WACA,OAAAZ,EAAA,SAGAJ,EAAAiB,KAAA,WACA,OAAAb,EAAA,SAGAJ,EAAAmB,QAAA,WACA,OAAAnB,EAAAkB,WAAAd,EAAA,YAGAJ,EAAAoB,aAAA,WACA,OAAApB,EAAAmB,WAAAf,EAAA,WAGAJ,EAAAqB,cAAA,WACA,OAAArB,EAAAmB,YAAAf,EAAA,WAGAJ,EAAAsB,WAAA,WACA,OAAAlB,EAAA,eAAAA,EAAA,SAAAA,EAAA,QAGAJ,EAAAuB,gBAAA,WACA,OAAAvB,EAAAsB,eAAAlB,EAAA,WAGAJ,EAAAwB,iBAAA,WACA,OAAAxB,EAAAsB,cAAAlB,EAAA,WAGAJ,EAAAkB,QAAA,WACA,OAAAd,EAAA,YAGAJ,EAAAyB,aAAA,WACA,OAAAzB,EAAAkB,WAAAd,EAAA,UAGAJ,EAAA0B,cAAA,WACA,OAAA1B,EAAAkB,WAAAd,EAAA,WAAAJ,EAAAyB,gBAGAzB,EAAA2B,KAAA,WACA,OAAAvB,EAAA,aAAAA,EAAA,cAAAA,EAAA,UAGAJ,EAAA4B,UAAA,WACA,OAAA5B,EAAA2B,QAAAvB,EAAA,WAGAJ,EAAA6B,WAAA,WACA,OAAA7B,EAAA2B,QAAAvB,EAAA,WAGAJ,EAAA8B,MAAA,WACA,OAAA1B,EAAA,UAGAJ,EAAA+B,QAAA,WACA,OAAArB,OAAAqB,SAAA,UAAAC,SAAAC,UAGAjC,EAAAkC,WAAA,WACA,MAAA,iBAAAxB,OAAAyB,SAGAnC,EAAAoC,OAAA,WACA,OAAApC,EAAAoB,gBAAApB,EAAAe,UAAAf,EAAAgB,QAAAhB,EAAAyB,gBAAAzB,EAAAuB,mBAAAvB,EAAA4B,aAAA5B,EAAA8B,SAGA9B,EAAAqC,OAAA,WACA,OAAArC,EAAAiB,QAAAjB,EAAAqB,iBAAArB,EAAAwB,oBAAAxB,EAAA0B,iBAAA1B,EAAA6B,cAGA7B,EAAAsC,QAAA,WACA,OAAAtC,EAAAqC,WAAArC,EAAAoC,UAGApC,EAAAuC,WAAA,WACA,IAAAC,EAAAD,EAAA,CACA,WACA,QACA,UACA,cACA,UACA,QACA,UACA,QACA,OACA,OACA,UACA,OACA,SACA,QACA,WAIA,IADAC,EAAA,EACAA,EAAAD,EAAAE,QAAA,CACA,GAAArC,EAAAmC,EAAAC,IACA,OAAA,EAEAA,IAEA,OAAA,GAGAxC,EAAA0C,SAAA,WACA,OAAA,EAAAhC,OAAAiC,YAAAjC,OAAAkC,YAGA5C,EAAA6C,UAAA,WACA,OAAAnC,OAAAiC,YAAAjC,OAAAkC,WAAA,GAQA5C,EAAA8C,WAAA,WAEA,OADApC,OAAAV,OAAAC,EACA8C,MAOA3C,EAAA,SAAA4C,GACA,OAAA,IAAAvC,EAAAwC,QAAAD,IAIA1C,EAAA,SAAA4C,GACA,IAAAC,EAEA,OADAA,EAAA,IAAAC,OAAAF,EAAA,KACA/C,EAAA+C,UAAAG,MAAAF,IAIAjD,EAAA,SAAAgD,GACA,IAAAI,EAAA,KACAhD,EAAA4C,KACAI,EAAAnD,EAAA+C,UAAAK,QAAA,aAAA,IACApD,EAAA+C,UAAAI,EAAA,IAAAJ,IAKA1C,EAAA,SAAA0C,GACA5C,EAAA4C,KACA/C,EAAA+C,UAAA/C,EAAA+C,UAAAK,QAAA,IAAAL,EAAA,MASAlD,EAAAc,MACAd,EAAAiB,OACAf,EAAA,mBACAF,EAAAe,SACAb,EAAA,qBACAF,EAAAgB,QACAd,EAAA,mBAEAF,EAAAmB,UACAnB,EAAAqB,gBACAnB,EAAA,kBAEAA,EAAA,kBAEAF,EAAAsB,aACAtB,EAAAwB,mBACAtB,EAAA,qBAEAA,EAAA,qBAEAF,EAAAkB,UACAlB,EAAA0B,gBACAxB,EAAA,kBACAF,EAAAyB,eACAvB,EAAA,kBAEAA,EAAA,WAEAF,EAAA2B,OACA3B,EAAA6B,aACA3B,EAAA,eAEAA,EAAA,eAEAF,EAAA8B,QACA5B,EAAA,gBACAF,EAAAkC,aACAhC,EAAA,eACAF,EAAAuC,aACArC,EAAA,cACAF,EAAAsC,WACApC,EAAA,WAGAF,EAAA+B,WACA7B,EAAA,WAOAG,EAAA,WACAL,EAAA6C,aACArC,EAAA,YACAN,EAAA,eAEAM,EAAA,aACAN,EAAA,cAOAK,EADAiD,OAAAC,UAAAC,eAAAC,KAAAjD,OAAA,uBACA,oBAEA,SAIAA,OAAAkD,iBACAlD,OAAAkD,iBAAArD,EAAAF,GAAA,GACAK,OAAAmD,YACAnD,OAAAmD,YAAAtD,EAAAF,GAEAK,OAAAH,GAAAF,EAGAA,IAEA,mBAAAyD,QAAA,iBAAAA,OAAAC,KAAAD,OAAAC,IACAD,OAAA,WACA,OAAA9D,IAEA,oBAAAgE,QAAAA,OAAAC,QACAD,OAAAC,QAAAjE,EAEAU,OAAAV,OAAAA,IAGA2D,KAAAZ", "file": "device.min.js", "sourcesContent": ["// Device.js\r\n// (c) 2014 <PERSON>\r\n// Device.js is freely distributable under the MIT license.\r\n// For all details and documentation:\r\n// http://matthewhudson.me/projects/device.js/\r\n\r\n(function() {\r\n\r\n  var device,\r\n    previousDevice,\r\n    addClass,\r\n    documentElement,\r\n    find,\r\n    handleOrientation,\r\n    hasClass,\r\n    orientationEvent,\r\n    removeClass,\r\n    userAgent;\r\n\r\n  // Save the previous value of the device variable.\r\n  previousDevice = window.device;\r\n\r\n  device = {};\r\n\r\n  // Add device as a global object.\r\n  window.device = device;\r\n\r\n  // The <html> element.\r\n  documentElement = window.document.documentElement;\r\n\r\n  // The client user agent string.\r\n  // Lowercase, so we can use the more efficient indexOf(), instead of Regex\r\n  userAgent = window.navigator.userAgent.toLowerCase();\r\n\r\n  // Main functions\r\n  // --------------\r\n\r\n  device.ios = function () {\r\n    return device.iphone() || device.ipod() || device.ipad();\r\n  };\r\n\r\n  device.iphone = function () {\r\n    return !device.windows() && find('iphone');\r\n  };\r\n\r\n  device.ipod = function () {\r\n    return find('ipod');\r\n  };\r\n\r\n  device.ipad = function () {\r\n    return find('ipad');\r\n  };\r\n\r\n  device.android = function () {\r\n    return !device.windows() && find('android');\r\n  };\r\n\r\n  device.androidPhone = function () {\r\n    return device.android() && find('mobile');\r\n  };\r\n\r\n  device.androidTablet = function () {\r\n    return device.android() && !find('mobile');\r\n  };\r\n\r\n  device.blackberry = function () {\r\n    return find('blackberry') || find('bb10') || find('rim');\r\n  };\r\n\r\n  device.blackberryPhone = function () {\r\n    return device.blackberry() && !find('tablet');\r\n  };\r\n\r\n  device.blackberryTablet = function () {\r\n    return device.blackberry() && find('tablet');\r\n  };\r\n\r\n  device.windows = function () {\r\n    return find('windows');\r\n  };\r\n\r\n  device.windowsPhone = function () {\r\n    return device.windows() && find('phone');\r\n  };\r\n\r\n  device.windowsTablet = function () {\r\n    return device.windows() && (find('touch') && !device.windowsPhone());\r\n  };\r\n\r\n  device.fxos = function () {\r\n    return (find('(mobile;') || find('(tablet;')) && find('; rv:');\r\n  };\r\n\r\n  device.fxosPhone = function () {\r\n    return device.fxos() && find('mobile');\r\n  };\r\n\r\n  device.fxosTablet = function () {\r\n    return device.fxos() && find('tablet');\r\n  };\r\n\r\n  device.meego = function () {\r\n    return find('meego');\r\n  };\r\n\r\n  device.cordova = function () {\r\n    return window.cordova && location.protocol === 'file:';\r\n  };\r\n\r\n  device.nodeWebkit = function () {\r\n    return typeof window.process === 'object';\r\n  };\r\n\r\n  device.mobile = function () {\r\n    return device.androidPhone() || device.iphone() || device.ipod() || device.windowsPhone() || device.blackberryPhone() || device.fxosPhone() || device.meego();\r\n  };\r\n\r\n  device.tablet = function () {\r\n    return device.ipad() || device.androidTablet() || device.blackberryTablet() || device.windowsTablet() || device.fxosTablet();\r\n  };\r\n\r\n  device.desktop = function () {\r\n    return !device.tablet() && !device.mobile();\r\n  };\r\n\r\n  device.television = function() {\r\n    var i, television = [\r\n      \"googletv\",\r\n      \"viera\",\r\n      \"smarttv\",\r\n      \"internet.tv\",\r\n      \"netcast\",\r\n      \"nettv\",\r\n      \"appletv\",\r\n      \"boxee\",\r\n      \"kylo\",\r\n      \"roku\",\r\n      \"dlnadoc\",\r\n      \"roku\",\r\n      \"pov_tv\",\r\n      \"hbbtv\",\r\n      \"ce-html\"\r\n    ];\r\n\r\n    i = 0;\r\n    while (i < television.length) {\r\n      if (find(television[i])) {\r\n        return true;\r\n      }\r\n      i++;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  device.portrait = function () {\r\n    return (window.innerHeight / window.innerWidth) > 1;\r\n  };\r\n\r\n  device.landscape = function () {\r\n    return (window.innerHeight / window.innerWidth) < 1;\r\n  };\r\n\r\n  // Public Utility Functions\r\n  // ------------------------\r\n\r\n  // Run device.js in noConflict mode,\r\n  // returning the device variable to its previous owner.\r\n  device.noConflict = function () {\r\n    window.device = previousDevice;\r\n    return this;\r\n  };\r\n\r\n  // Private Utility Functions\r\n  // -------------------------\r\n\r\n  // Simple UA string search\r\n  find = function (needle) {\r\n    return userAgent.indexOf(needle) !== -1;\r\n  };\r\n\r\n  // Check if documentElement already has a given class.\r\n  hasClass = function (className) {\r\n    var regex;\r\n    regex = new RegExp(className, 'i');\r\n    return documentElement.className.match(regex);\r\n  };\r\n\r\n  // Add one or more CSS classes to the <html> element.\r\n  addClass = function (className) {\r\n    var currentClassNames = null;\r\n    if (!hasClass(className)) {\r\n      currentClassNames = documentElement.className.replace(/^\\s+|\\s+$/g, '');\r\n      documentElement.className = currentClassNames + \" \" + className;\r\n    }\r\n  };\r\n\r\n  // Remove single CSS class from the <html> element.\r\n  removeClass = function (className) {\r\n    if (hasClass(className)) {\r\n      documentElement.className = documentElement.className.replace(\" \" + className, \"\");\r\n    }\r\n  };\r\n\r\n  // HTML Element Handling\r\n  // ---------------------\r\n\r\n  // Insert the appropriate CSS class based on the _user_agent.\r\n\r\n  if (device.ios()) {\r\n    if (device.ipad()) {\r\n      addClass(\"ios ipad tablet\");\r\n    } else if (device.iphone()) {\r\n      addClass(\"ios iphone mobile\");\r\n    } else if (device.ipod()) {\r\n      addClass(\"ios ipod mobile\");\r\n    }\r\n  } else if (device.android()) {\r\n    if (device.androidTablet()) {\r\n      addClass(\"android tablet\");\r\n    } else {\r\n      addClass(\"android mobile\");\r\n    }\r\n  } else if (device.blackberry()) {\r\n    if (device.blackberryTablet()) {\r\n      addClass(\"blackberry tablet\");\r\n    } else {\r\n      addClass(\"blackberry mobile\");\r\n    }\r\n  } else if (device.windows()) {\r\n    if (device.windowsTablet()) {\r\n      addClass(\"windows tablet\");\r\n    } else if (device.windowsPhone()) {\r\n      addClass(\"windows mobile\");\r\n    } else {\r\n      addClass(\"desktop\");\r\n    }\r\n  } else if (device.fxos()) {\r\n    if (device.fxosTablet()) {\r\n      addClass(\"fxos tablet\");\r\n    } else {\r\n      addClass(\"fxos mobile\");\r\n    }\r\n  } else if (device.meego()) {\r\n    addClass(\"meego mobile\");\r\n  } else if (device.nodeWebkit()) {\r\n    addClass(\"node-webkit\");\r\n  } else if (device.television()) {\r\n    addClass(\"television\");\r\n  } else if (device.desktop()) {\r\n    addClass(\"desktop\");\r\n  }\r\n\r\n  if (device.cordova()) {\r\n    addClass(\"cordova\");\r\n  }\r\n\r\n  // Orientation Handling\r\n  // --------------------\r\n\r\n  // Handle device orientation changes.\r\n  handleOrientation = function () {\r\n    if (device.landscape()) {\r\n      removeClass(\"portrait\");\r\n      addClass(\"landscape\");\r\n    } else {\r\n      removeClass(\"landscape\");\r\n      addClass(\"portrait\");\r\n    }\r\n  };\r\n\r\n  // Detect whether device supports orientationchange event,\r\n  // otherwise fall back to the resize event.\r\n  if (Object.prototype.hasOwnProperty.call(window, \"onorientationchange\")) {\r\n    orientationEvent = \"orientationchange\";\r\n  } else {\r\n    orientationEvent = \"resize\";\r\n  }\r\n\r\n  // Listen for changes in orientation.\r\n  if (window.addEventListener) {\r\n    window.addEventListener(orientationEvent, handleOrientation, false);\r\n  } else if (window.attachEvent) {\r\n    window.attachEvent(orientationEvent, handleOrientation);\r\n  } else {\r\n    window[orientationEvent] = handleOrientation;\r\n  }\r\n\r\n  handleOrientation();\r\n\r\n  if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\r\n    define(function() {\r\n      return device;\r\n    });\r\n  } else if (typeof module !== 'undefined' && module.exports) {\r\n    module.exports = device;\r\n  } else {\r\n    window.device = device;\r\n  }\r\n\r\n}).call(this);"]}