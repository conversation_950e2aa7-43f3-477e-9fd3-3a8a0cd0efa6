<style>
    .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-heading .menu-icon.svg-icon svg g [fill], .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-link .menu-icon.svg-icon svg g [fill] {
        -webkit-transition: fill .3s ease;
        transition: fill .3s ease;
        fill: #b5b5c3;
    }
    .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-heading, .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-link {
        background-color: white;
    }
    .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-heading .menu-arrow, .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-link .menu-arrow {
        color: #000000;
    }
    .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-heading .menu-text, .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-link .menu-text {
        color: #000000;
    }
    .select2-container--default .select2-selection--multiple, .select2-container--default .select2-selection--single {
        background-color: #f3f6f9;
        border: 1px solid #f3f6f9;
    }
    .aside-menu .menu-nav>.menu-item .menu-submenu .menu-item.menu-item-active>.menu-heading, .aside-menu .menu-nav>.menu-item .menu-submenu .menu-item.menu-item-active>.menu-link {
        background-color: #2466D3;
    }
    .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-heading, .aside-menu .menu-nav>.menu-item.menu-item-open>.menu-link {
        background-color: #2466D3;
    }
    .aside-menu .menu-nav>.menu-item.menu-item-active>.menu-heading, .aside-menu .menu-nav>.menu-item.menu-item-active>.menu-link {
        background-color: #2466D3;
    }
    .aside-menu .menu-nav>.menu-item:not(.menu-item-parent):not(.menu-item-open):not(.menu-item-here):not(.menu-item-active):hover {
        background-color: #2466D3;
        -webkit-transition: background-color .3s;
        transition: background-color .3s;
    }
    .aside-menu .menu-nav>.menu-item.menu-item-active {
        background-color: #2466D3;
        -webkit-transition: background-color .3s;
        transition: background-color .3s;
    }
    .aside-menu .menu-nav>.menu-item:not(.menu-item-parent):not(.menu-item-open):not(.menu-item-here):not(.menu-item-active):hover {
        background-color: #2466D3;
        -webkit-transition: background-color .3s;
        transition: background-color .3s;
    }
    .aside-menu .menu-nav>.menu-item:not(.menu-item-parent):not(.menu-item-open):not(.menu-item-here):not(.menu-item-active):hover>.menu-heading, .aside-menu .menu-nav>.menu-item:not(.menu-item-parent):not(.menu-item-open):not(.menu-item-here):not(.menu-item-active):hover>.menu-link {
        background-color: #2466D3;
    }
    .aside-menu .menu-nav>.menu-item:not(.menu-item-parent):not(.menu-item-open):not(.menu-item-here):not(.menu-item-active):hover>.menu-heading, .aside-menu .menu-nav>.menu-item:not(.menu-item-parent):not(.menu-item-open):not(.menu-item-here):not(.menu-item-active):hover>.menu-link {
        background-color: #2466D3;
    }
    .aside-menu .menu-nav>.menu-item .menu-submenu .menu-item:not(.menu-item-parent):not(.menu-item-open):not(.menu-item-here):not(.menu-item-active):hover {
        background-color: #2466D3;
        -webkit-transition: background-color .3s;
        transition: background-color .3s;
    }
    .aside-menu .menu-nav>.menu-item .menu-submenu .menu-item:not(.menu-item-parent):not(.menu-item-open):not(.menu-item-here):not(.menu-item-active):hover>.menu-heading, .aside-menu .menu-nav>.menu-item .menu-submenu .menu-item:not(.menu-item-parent):not(.menu-item-open):not(.menu-item-here):not(.menu-item-active):hover>.menu-link {
        background-color: #2466D3;
    }
    .menu2 {
        padding-left: 0px;
    }
     .menu2 ul {
         list-style-type: none;
     }

    .menu2 li {
        display: inline-block;
        margin-right: 10px;
        border-bottom: 2px solid transparent;
        --tw-border-opacity: 1;
    }
    .menu2 li div {
        display: inline-block; /* Hiển thị hàng ngang */
        padding-bottom: 1rem;
    }

    .menu2 li.active {
        border-bottom-color: #1857BD; /* Màu của border dưới khi được chọn */
    }

</style>
