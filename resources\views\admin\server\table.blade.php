<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_server',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatableServer;
        $(document).ready(function() {
            DatatableServer = window.LaravelDataTables["datatable_server"];
        });
        $(document).on('click', '.view_server', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            axios.get('servers/' + id)
                .then((response) => {
                    if (response.data.status) {
                        let server = response.data.data;
                        $('#update_id').val(server.id);
                        $('#update_ip_address').val(server.ip_address);
                        $('#update_city').val(server.city);
                        $('#update_state').val(server.state);
                    } else {
                        mess_error(response.data.title, response.data.message)
                    }
                });
        });
    </script>
@endpush
