<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_transaction',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatableTransaction;
        $(document).ready(function() {
            DatatableTransaction = window.LaravelDataTables["datatable_transaction"];
        });
    </script>
    <script>
        $(document).ready(function(){
            $("#model_export_transaction").click(function(){
                window.location.href = "{{ url('admin/transaction/export') }}";
            });
        });
        $(document).on('click', '.change_status', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: '{{__('<PERSON>ạn có chắc chắn muốn thay đổi trạng thái tài khoản ngâng hàng không?')}}',
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.post('/admin/transactions/change-status')
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.title, response.data.message)
                                setTimeout(() => {
                                    window.location.reload();
                                }, 2000);
                            } else {
                                mess_error(response.data.title, response.data.message)
                            }
                        });
                }
            });
        });

    </script>
@endpush
