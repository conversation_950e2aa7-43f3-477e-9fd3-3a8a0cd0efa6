<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\ServerDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Create\CreateServerRequest;
use App\Http\Requests\Admin\Update\UpdateServerRequest;
use App\Jobs\CreateServerProxy;
use App\Repositories\ProxyRepository;
use App\Repositories\ServerRepository;
use App\Services\ProxyService;

class ServerController extends Controller
{
    private ProxyService $proxyService;

    private ServerRepository $server;

    private ProxyRepository $proxy;

    public function __construct(
        ProxyService $proxyService,
        ServerRepository $serverRepository,
        ProxyRepository $proxyRepository,
    ) {
        $this->proxyService = $proxyService;
        $this->server = $serverRepository;
        $this->proxy = $proxyRepository;
    }

    public function index(ServerDataTable $serverDataTable)
    {
        return $serverDataTable->render('admin.server.index');
    }

    public function store(CreateServerRequest $request): \Illuminate\Http\JsonResponse
    {
        $input = $request->all();
        $result = $this->json2array($this->proxyService->check_connect($input));
        if (! $result['original']['status']) {
            return $this->error($result['original']['message']);
        }
        $query_create = $this->server->create($input);
        if ($query_create) {
            $this->dispatch(new CreateServerProxy($query_create->toArray()));

            return $this->success('Thêm server thành công');
        }

        return $this->error();
    }

    public function show(int $id): \Illuminate\Http\JsonResponse
    {
        $response = $this->server->find($id, ['id', 'ip_address', 'state', 'city']);
        if ($response) {
            return $this->success(data: $response);
        }

        return $this->error();
    }

    public function update(int $id, UpdateServerRequest $request): \Illuminate\Http\JsonResponse
    {
        $input = $request->all();
        $query_find = $this->server->find($id);
        $result = $this->json2array($this->proxyService->check_connect($query_find));
        if (! $result['original']['status']) {
            return $this->error($result['original']['message']);
        }
        $query_update = $query_find->update($input);
        if ($query_update) {
            return $this->success('Cập nhật server thành công');
        }

        return $this->error();
    }

    public function destroy(int $id): \Illuminate\Http\JsonResponse
    {
        $query = $this->proxy->query()
            ->where('server_id', '=', $id)
            ->has('user')
            ->exists();
        if ($query) {
            return $this->error('Server đang có proxy mà khách thuê');
        }
        $query_delete = $this->server->delete($id);
        if ($query_delete) {
            return $this->success('Xoá server thành công');
        }

        return $this->error();
    }
}
