<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\UserDataTable;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UserController extends Controller
{
    private UserRepository $user;

    public function __construct(
        UserRepository $userRepository,
    ) {
        $this->user = $userRepository;
    }

    public function index(UserDataTable $userDataTable)
    {
        return $userDataTable->render('admin.user.index');
    }

    public function status(int $id): JsonResponse
    {
        $query = $this->user->find($id);
        if ($query) {
            if ($query['status'] === User::ACTIVE) {
                $query->update(['status' => User::DISABLE]);
            } else {
                $query->update(['status' => User::ACTIVE]);
            }

            return $this->success('Cập nhật trạng thái người dùng thành công');
        }

        return $this->error();
    }

    public function role(int $id): JsonResponse
    {
        $query = $this->user->find($id);
        if ($query) {
            if ($query['type'] === User::ADMIN) {
                $query->update(['type' => User::USER]);
            } else {
                $query->update(['type' => User::ADMIN]);
            }

            return $this->success('Cập nhật quyền người dùng thành công');
        }

        return $this->error();
    }

    public function destroy(int $id): JsonResponse
    {
        $query = $this->user->delete($id);
        if ($query) {
            return $this->success('Xoá người dùng thành công');
        }

        return $this->error();
    }

    public function update(int $id, Request $request): \Illuminate\Http\JsonResponse
    {
        $input = $request->all();
        if ($request->has('password') && $request->input('password') !== null) {
            $input['password'] = bcrypt($request->input('password'));
        } else {
            unset($input['password']);
        }

        $query = $this->user->find($id);
        if ($query) {
            $query->update($input);

            return $this->success('Cập nhật thành công');
        }

        return $this->error();
    }

    public function show(int $id): \Illuminate\Http\JsonResponse
    {
        $response = $this->user->find($id);
        if ($response) {
            return $this->success(data: $response);
        }

        return $this->error();
    }
}
