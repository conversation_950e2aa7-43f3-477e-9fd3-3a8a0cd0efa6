{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-gd": "*", "ext-pdo": "*", "genealabs/laravel-model-caching": "^11.0", "google/apiclient": "^2.13", "google/cloud": "^0.199", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.1.5", "laravel/horizon": "^5.20", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "league/csv": "^9.10", "maatwebsite/excel": "^3.1", "predis/predis": "^2.2", "yajra/laravel-datatables-buttons": "^10.0", "yajra/laravel-datatables-oracle": "^10.3"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan horizon:publish --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}