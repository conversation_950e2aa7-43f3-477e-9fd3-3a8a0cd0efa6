<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_package',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatablePackage;
        $(document).ready(function() {
            DatatablePackage = window.LaravelDataTables["datatable_package"];
        });
        $(document).on('click', '.view_package', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            var resetTimeField = document.getElementById('update_reset_timeField');
            var expireTimeField = document.getElementById('update_expire_timeField');
            axios.get('package/' + id)
                .then((response) => {
                    if (response.data.status) {
                        let package = response.data.data;
                        $('#update_id').val(package.id);
                        $('#update_name').val(package.name);
                        $('#update_description').val(package.description);
                        $('#update_type').val(package.type);
                        $('#update_duration').val(package.duration);
                        $('#update_price').val(package.price);
                        $('#update_quantity').val(package.quantity);
                        $('#update_reset_time').val(package.reset_time);
                        $('#update_reset_time2').val(package.reset_time);
                        $('#update_expire_time').val(package.expire_time);
                        $('#update_expire_time2').val(package.expire_time);
                        if (package.type ==='vpn')
                        {
                            resetTimeField.style.display = 'none';
                            expireTimeField.style.display = 'none';
                        }else
                        {
                            resetTimeField.style.display = 'block';
                            expireTimeField.style.display = 'block';
                        }
                    } else {
                        mess_error(response.data.title, response.data.message)
                    }
                });
        });
    </script>
@endpush
