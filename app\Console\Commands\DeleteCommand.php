<?php

namespace App\Console\Commands;

use App\Models\Server;
use App\Services\ProxyService;
use Illuminate\Console\Command;

class DeleteCommand extends Command
{
    protected $signature = 'app:delete';

    public function handle(): void
    {
        $proxies = new ProxyService();
        $servers = Server::all();
        foreach ($servers as $server) {
            $proxies->delete_interface($server->toArray());
        }
    }
}
