<!DOCTYPE html>
<html lang=en>
<head>
    <base href>
    <meta charset=utf-8/>
    <title>{{ __('Hóa đơn').' #'.$data['invoice_code'] }}</title>
    <meta name=csrf-token content="{{ csrf_token() }}">
    <meta name=viewport content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <link href="//fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" rel="stylesheet"/>
    <link href="{{ asset('/assets/css/themes/layout/header/base/light.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('/assets/css/themes/layout/header/menu/light.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('/assets/css/themes/layout/brand/light.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('/assets/css/themes/layout/aside/light.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('/assets/plugins/global/plugins.bundle.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('/assets/css/style.bundle.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('/assets/css/style.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('/assets/plugins/custom/datatables/datatables.bundle.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('images/favicon.ico') }}" rel="shortcut icon"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">

</head>
<body id="kt_body" class="page-loading-enabled page-loading  subheader-enabled subheader-fixed aside-enabled aside-minimize-hoverable page-loading" style="background-color: #F3F6F9">
<div class="page-loader page-loader-logo">
    <div class="spinner spinner-primary"></div>
</div>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper" id=kt_wrapper>
            <div class="content d-flex flex-column flex-column-fluid" id=kt_content>
                <div class="d-flex flex-column-fluid">
                    <div class=container>
                        <div id="kt_content_container" class="d-flex flex-column-fluid align-items-start container-xxl">
                            <div class="content flex-row-fluid" id="kt_content">
                                <div class="card">
                                    <div class="card-body p-lg-20">
                                        <div class="d-flex flex-column flex-xl-row">
                                            <div class="col-8 flex-lg-row-fluid me-xl-18 mb-10 mb-xl-0">
                                                <div class="mt-n1">
                                                    <div class="pb-10">
                                                        <a href="https://proxytn.com">
                                                            <img alt="Logo" id="logo-invoice" class="logo-main h-50px h-lg-60px" src="{{ asset('images/site_logo/logo_2.png') }}" />
                                                        </a>
                                                    </div>
                                                    <div class="m-0">
                                                        <div class="font-weight-bold fs-3 text-gray-800 mb-8 display-4">{{ __('Hóa đơn') }} #{{ $data['invoice_code'] }}</div>
                                                        <div class="row g-5 mb-11">
                                                            <div class="col-sm-6">
                                                                <div class="font-weight-normal fs-7 text-gray-600 mb-1">{{ __('Ngày tạo:') }}</div>
                                                                <div class="font-weight-bold fs-6 text-gray-800">{{ \Carbon\Carbon::parse($data['created_at'])->format('d-m-Y H:i:s') }}</div>
                                                            </div>
                                                        </div>
                                                        <div class="row g-5 mb-12">
                                                            <div class="col-sm-6">
                                                                <div class="font-weight-normal fs-7 text-gray-600 mb-1">{{ __('Tạo bởi') }}:</div>
                                                                <div class="font-weight-bold fs-6 text-gray-800">ProxyTN</div>
                                                                <div class="font-weight-normal fs-7 text-gray-600">
                                                                    <br>Email: <EMAIL>
                                                                </div>
                                                            </div>

                                                            <div class="col-sm-6">
                                                                <div class="font-weight-normal fs-7 text-gray-600 mb-1">{{ __('Khách hàng') }}:</div>
                                                                <div class="font-weight-bold fs-6 text-gray-800">{{ $data['user_name'] }}</div>
                                                                <div class="font-weight-normal fs-7 text-gray-600">
                                                                    {{ $data['user_address'] }}
                                                                    <br>Email: {{ $data['user_email'] }}
                                                                    <br>{{ __('SĐT') }}: {{ $data['user_phone'] }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <div class="table-responsive border-bottom mb-9">
                                                                <table class="table mb-3">
                                                                    <thead>
                                                                    <tr class="border-bottom fs-6 font-weight-bold text-muted">
                                                                        <th class="min-w-175px pb-2">{{ __('Tên gói') }}</th>
                                                                        <th class="min-w-70px text-end pb-2" style="margin-left: auto;">{{ __('Loại') }}</th>
                                                                        <th class="min-w-80px text-end pb-2" style="margin-left: auto;">{{ __('Thời gian') }}</th>
                                                                        <th class="min-w-100px text-end pb-2" style="margin-left: auto;">{{ __('Số lượng') }}</th>
                                                                        <th class="min-w-100px text-end pb-2" style="margin-left: auto;">{{ __('Đơn giá') }}</th>
                                                                    </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                    <tr class="font-weight-bold text-gray-700 fs-5 text-end" style="margin-left: auto;">
                                                                        <td class="d-flex align-items-center pt-6">
                                                                            <div class="d-flex align-items-center">
                                                                                <div class="d-flex flex-column flex-grow-1 my-lg-0 my-2 pr-3">
                                                                                    <span class="text-dark font-weight-bold text-hover-primary fs-5">{{ $data['name'] }}</span>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td class="pt-6">
                                                                            <div class="d-flex align-items-center">
                                                                                <div class="symbol symbol-30px me-5"></div>
                                                                                <div class="d-flex flex-column flex-grow-1 my-lg-0 my-2 pr-3">
                                                                                    <span class="text-gray-600 text-hover-primary fs-5">{{ $data['type'] }}</span>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td class="pt-6">
                                                                            <div class="d-flex align-items-center">
                                                                                <div class="symbol symbol-30px me-5"></div>
                                                                                <div class="d-flex flex-column flex-grow-1 my-lg-0 my-2 pr-3">
                                                                                    <span class="text-gray-600 text-hover-primary fs-5">{{ $data['duration'] }} ngày</span>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td class="pt-6">
                                                                            <div class="d-flex align-items-center">
                                                                                <div class="symbol symbol-30px me-5"></div>
                                                                                <div class="d-flex flex-column flex-grow-1 my-lg-0 my-2 pr-3">
                                                                                    <span class="text-gray-600 text-hover-primary fs-5">{{ $data['quantity'] }}</span>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td class="pt-6">
                                                                            <div class="d-flex align-items-center">
                                                                                <div class="symbol symbol-30px me-5"></div>
                                                                                <div class="d-flex flex-column flex-grow-1 my-lg-0 my-2 pr-3">
                                                                                    <span class="text-gray-600 text-hover-primary fs-5">{{ number_format($data['price'], 0, ',', '.') }} đ</span>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col=4 m-0">
                                                <div class="d-print-none border border-dashed border-gray-300 card-rounded h-lg-100 min-w-md-350px p-9 bg-lighten">
                                                    <div class="mb-6 justify-content-end">
                                                        <div class="mw-300px">
                                                            <div class="d-flex flex-stack">
                                                                <div class="font-weight-normal text-gray-600 fs-7" style="align-items: center;display: flex;}">{{ __('Trạng thái') }}:</div>
                                                                @if($data['order_status'] == 0)
                                                                    <span class="btn btn-warning" style="margin-left: auto;">{{ __('Đang xử lý') }}</span>
                                                                @elseif($data['order_status'] == 1)
                                                                    <span class="btn btn-success" style="margin-left: auto;">{{ __('Thành công') }}</span>
                                                                @else
                                                                    <span class="btn btn-danger" style="margin-left: auto;">{{ __('Đã hủy') }}</span>
                                                                @endif
                                                            </div><br>
                                                            <div class="d-flex flex-stack">
                                                                <div class="font-weight-normal text-gray-600 fs-7">Email:</div>
                                                                <div class="text-end font-weight-bold text-gray-800 fs-6" style="margin-left: auto;">{{ $data['user_email'] }}</div>
                                                            </div><br>

                                                            <div class="d-flex flex-stack">
                                                                <div class="font-weight-normal text-gray-600 fs-7">{{ __('Số điện thoại') }}:</div>
                                                                <div class="text-end font-weight-bold text-gray-800 fs-6" style="margin-left: auto;">{{ $data['user_phone'] }}
                                                                </div>
                                                            </div><br>
                                                        </div>
                                                    </div>
                                                    <h6 class="mb-8 font-weight-bolder text-gray-600 text-hover-primary">{{ __('CHI TIẾT') }}</h6>
                                                    <div class="mb-6 justify-content-end">
                                                        <div class="mw-300px">
                                                            <div class="d-flex flex-stack">
                                                                <div class="font-weight-normal pe-10 text-gray-600 fs-7">{{ __('Tổng phụ') }}:</div>
                                                                <div class="text-end font-weight-bold fs-6 text-gray-800" style="margin-left: auto;">{{ number_format($data['price'], 0, ',', '.') }} đ</div>
                                                            </div>
                                                            {{--                                        <div class="d-flex flex-stack">--}}
                                                            {{--                                            <div class="font-weight-normal pe-10 text-gray-600 fs-7">VAT ({{ $invoice['vat_percent'] }}%)</div>--}}
                                                            {{--                                            <div class="text-end font-weight-bold fs-6 text-gray-800">--}}
                                                            {{--                                                {{ number_format($invoice['total_amount'] * $invoice['vat_percent'] / 100, 0, ',', '.') }} đ--}}
                                                            {{--                                            </div>--}}
                                                            {{--                                        </div>--}}
                                                            <div class="d-flex flex-stack">
                                                                <div class="font-weight-normal pe-10 text-gray-600 fs-7">{{ __('Giảm giá') }}</div>
                                                                <div class="text-end font-weight-bold fs-6 text-gray-800" style="margin-left: auto;">0 đ</div>
                                                            </div>
                                                            <div class="d-flex flex-stack">
                                                                <div class="font-weight-bold text-decoration-underline pe-10 text-gray-800 fs-7">
                                                                    {{ __('Total') }}</div>
                                                                <div class="text-end font-weight-bold fs-6 text-gray-800" style="margin-left: auto;">
                                                                    {{
                                                                        number_format(
                                                                            $data['price'],
                                                                            0,
                                                                            ',',
                                                                            '.'
                                                                        )
                                                                    }} đ
                                                                </div>
                                                            </div>
                                                            <br>
                                                            <div class="font-weight-normal text-gray-600 fs-7">
                                                                {{ __('Với việc thanh toán hóa đơn này, bạn đồng ý với các điều khoản, chính sách của ProxyTN') }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script>let KTAppSettings = { "breakpoints": { "sm": 576, "md": 768, "lg": 992, "xl": 1200, "xxl": 1400 }, "colors": { "theme": { "base": { "white": "#ffffff", "primary": "#3699FF", "secondary": "#E5EAEE", "success": "#1BC5BD", "info": "#8950FC", "warning": "#FFA800", "danger": "#F64E60", "light": "#E4E6EF", "dark": "#181C32" }, "light": { "white": "#ffffff", "primary": "#E1F0FF", "secondary": "#EBEDF3", "success": "#C9F7F5", "info": "#EEE5FF", "warning": "#FFF4DE", "danger": "#FFE2E5", "light": "#F3F6F9", "dark": "#D6D6E0" }, "inverse": { "white": "#ffffff", "primary": "#ffffff", "secondary": "#3F4254", "success": "#ffffff", "info": "#ffffff", "warning": "#ffffff", "danger": "#ffffff", "light": "#464E5F", "dark": "#ffffff" } }, "gray": { "gray-100": "#F3F6F9", "gray-200": "#EBEDF3", "gray-300": "#E4E6EF", "gray-400": "#D1D3E0", "gray-500": "#B5B5C3", "gray-600": "#7E8299", "gray-700": "#5E6278", "gray-800": "#3F4254", "gray-900": "#181C32" } }, "font-family": "Poppins" };</script>
<script src="{{ asset('assets/plugins/global/plugins.bundle.js') }}"></script>
<script src="{{ asset('assets/plugins/custom/prismjs/prismjs.bundle.js') }}"></script>
<script src="{{ asset('assets/js/scripts.bundle.js') }}"></script>
<script src="{{ asset('assets/plugins/custom/datatables/datatables.bundle.js') }}"></script>
<script src="{{ asset('assets/js/scripts.js') }}"></script>
<script src="{{ asset('assets/js/axios.js') }}"></script>
@include('component.user.script')
</html>
