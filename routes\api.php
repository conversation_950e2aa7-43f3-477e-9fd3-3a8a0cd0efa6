<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\LocationController;
use App\Http\Controllers\API\ProxyController;
use App\Http\Controllers\API\ServerController;
use App\Http\Controllers\API\VpnController;
use Illuminate\Support\Facades\Route;

//Auth
Route::post('/login', [AuthController::class, 'login']);

Route::middleware('auth:sanctum')->group(function () {
    //Auth
    Route::get('/logout', [AuthController::class, 'logout']);
    //Register
    Route::post('/register', [AuthController::class, 'register']);
    //Forgot Password
    Route::post('/forgot-pass', [AuthController::class, 'forgot_pass']);
    //Reset Password
    Route::post('/reset-pass', [AuthController::class, 'reset_pass']);

    //Vpn
    Route::get('/vpn', [VpnController::class, 'index']);
    Route::get('/vpn/{id}', [VpnController::class, 'show']);
    Route::get('/vpn/package', [VpnController::class, 'package']);
    Route::get('/vpn/random', [VpnController::class, 'random']);
    Route::get('/vpn/allservers', [VpnController::class, 'allservers']);
});

Route::get('get-country', [LocationController::class, 'getCountry']);
Route::get('get-state', [LocationController::class, 'getState']);
Route::get('get-city', [LocationController::class, 'getCity']);

Route::get('get-proxy', [ProxyController::class, 'getProxy']);
Route::get('reset-proxy', [ProxyController::class, 'resetProxy']);
Route::get('status-proxy', [ProxyController::class, 'statusProxy']);

Route::patch('servers/{id}', [ServerController::class, 'patch']);
