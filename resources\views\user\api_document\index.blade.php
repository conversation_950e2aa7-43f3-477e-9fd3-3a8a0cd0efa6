@extends('layout.user')
@section('content')
    <div class="card card-custom">
        <div class="card-header flex-wrap py-5">
            <div class="card-title">
                <h3 class="card-label">{{ __('Tài liệu API') }}
                    <span class="d-block text-muted pt-2 font-size-sm">{{ __('Quản lý danh sách Tài liệu API') }}</span>
                </h3>
            </div>
        </div>
        <div class="card-body">
            <div id="apiOptions" class="row">
                <div class="col-6">
                    <button onclick="toggleAPI('proxy')" class="btn btn-primary w-100" id="api_proxy_btn">API Proxy</button>
                </div>
                <div class="col-6">
                    <button onclick="toggleAPI('user')" class="btn btn-primary w-100" id="api_user_btn" style="opacity: 0.5">API User</button>
                </div>
            </div>
            <div id="proxyAPIOptions" style="display: block; margin-top: 10px">
                <button data-toggle="collapse" data-target="#api_get_proxy" class="btn btn-secondary w-100 m-3 " >API Lấy Proxy</button>
                <div id="api_get_proxy" class="collapse m-3 ">
                   URL API: <code>https://proxytn.com/api/get-proxy?api_key=API_KEY</code>
                    Trong đó API_KEY lấy tại danh sách proxy đã mua.<br>
<code>
    <pre>
    // Khi lấy thành công
    Response
        {
            "success": true,
            "data":
                {
                "type": "proxy",
                "ip_address": "***********",
                "port": "2036",
                "username": "username1",
                "password": "password2",
                "country": "VietNam",
                "city": "Ha Noi",
                "state": "Cau Giay"
                "expired_at": "Không giới hạn",
                "reset_at": "Không giới hạn"
                 }
        }
    // Trường hợp thất bại
        {
            "success": "false",
            "error": "Unauthorized"
        }
    // Trường hợp khác

        {
            "success": true,
            "data":
                {
                "type": "proxy",
                "ip_address": "***********",
                "port": "2036",
                "username": "username1",
                "password": "password2",
                "country": "VietNam",
                "city": "Ha Noi",
                "state": "Cau Giay"
                "expired_at": "Đã hết hạn: 1 giờ, 2 phút, 30 giây",
                "reset_at": "Được đổi Ip: 0 giờ, 2 phút, 30 giây"
                 }
        }
    </pre>
</code>

                </div>
                <button data-toggle="collapse" data-target="#api_switch_proxy" class="btn btn-secondary w-100 m-3">API Reset Proxy</button>
                <div id="api_switch_proxy" class="collapse m-3 ">
                    URL API: <code>https://proxytn.com/api/reset-proxy?api_key=API_KEY</code>
                    Trong đó API_KEY lấy tại danh sách proxy đã mua.<br>
                    <code>
    <pre>
    // Khi xoay thành công
    Response
        {
            "status": true,
            "title": "Thành công",
            "message": "Đổi IP thành công vui lòng đợi trong ít giây",
            "data":
                    {
                    "type": "proxy",
                    "ip_address": "***********",
                    "port": "2036",
                    "username": "username1",
                    "password": "password2",
                    "country": "VietNam",
                    "city": "Ha Noi",
                    "state": "Cau Giay"
                     }
        }
    </pre>
                    </code>
                <hr>
                    <code>
    <pre>
    //  Lỗi khi chờ giới hạn thời gian xoay
    Response
        Response
        {
            "status": false,
            "title": "Thất bại",
            "message": "Vui lòng đợi 1 phút 71 giây để có thể đổi IP khác",
            "data": null
        }
    </pre>
                    </code>
                    <hr>
                    <code>
    <pre>
    //  Lỗi khi sai api_key
    Response
        {
            "status": false,
            "title": "Thất bại",
            "message": "Có lỗi xảy ra, vui lòng kiểm tra lại",
            "data": null
        }

    // Hoặc
        {
            "success": false,
            "error": "Unauthorized"
        }
    </pre>
                    </code>
                </div>
                <button data-toggle="collapse" data-target="#api_status_proxy" class="btn btn-secondary w-100 m-3">API kiểm tra Proxy</button>
                <div id="api_status_proxy" class="collapse m-3 ">
                    URL API: <code>https://proxytn.com/api/status-proxy?api_key=API_KEY</code>
                    Trong đó API_KEY lấy tại danh sách proxy đã mua.<br>
                    <code>
    <pre>
    // Khi sẵn sàng
    Response
        {
            "status": true,
            "title": "Thành công",
            "message": "Proxy sẵn sàng kết nối",
        }
    </pre>
                    </code>
                    <code>
    <pre>
    // Khi không kết nối được
    Response
        {
            "status": true,
            "title": "Thất bại",
            "message": "Đang không kết nối được tới proxy",
        }
    </pre>
                    </code>
                    <hr>
                    <code>
    <pre>
    //  Lỗi khi sai api_key
    Response
        {
            "status": false,
            "title": "Thất bại",
            "message": "Có lỗi xảy ra, vui lòng kiểm tra lại",
            "data": null
        }

    // Hoặc
        {
            "success": false,
            "error": "Unauthorized"
        }
    </pre>
                    </code>
                </div>
            </div>
            <div id="userAPIOptions" style="display: none;">
                <button data-toggle="collapse" data-target="#api_get_list_proxy" class="btn btn-secondary w-100 m-3 " >API lấy danh sách Proxy</button>
                <div id="api_get_list_proxy" class="collapse m-3 ">
                    URL API: <code>https://proxytn.com/api/get-proxis?user_api_key=USER_API_KEY</code>
                    Trong đó USER_API_KEY lấy tại thông tin Hồ sơ của người dùng.<br>
                    <code>
    <pre>
    // Khi lấy thành công
    Response
        {
            "success": true,
            "data":
                {
                "type": "proxy",
                "ip_address": "***********",
                "port": "2036",
                "username": "username1",
                "password": "password2",
                "country": "VietNam",
                "city": "Ha Noi",
                "state": "Cau Giay"
                 }
        }
    </pre>
                    </code>
                </div>
            </div>
        </div>
    </div>


    </div>

    <script>
        function toggleAPI(apiType) {
            if (apiType === 'proxy') {
                document.getElementById('proxyAPIOptions').style.display = 'block';
                document.getElementById('userAPIOptions').style.display = 'none';
                document.getElementById('api_proxy_btn').style.opacity = '1';
                document.getElementById('api_user_btn').style.opacity = '0.5';
            } else if (apiType === 'user') {
                document.getElementById('proxyAPIOptions').style.display = 'none';
                document.getElementById('userAPIOptions').style.display = 'block';
                document.getElementById('api_user_btn').style.opacity = '1';
                document.getElementById('api_proxy_btn').style.opacity = '0.5';

            }
        }
    </script>
@endsection
