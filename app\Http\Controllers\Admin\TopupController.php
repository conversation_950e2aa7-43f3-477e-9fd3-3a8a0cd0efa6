<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\TopupDataTable;
use App\Http\Controllers\Controller;
use App\Models\Affiliate;
use App\Models\Topup;
use App\Models\User;
use App\Services\BalanceService;

class TopupController extends Controller
{
    private BalanceService $balance;

    public function __construct(
        BalanceService $balanceService,
    ) {
        $this->balance = $balanceService;
    }

    public function index(TopupDataTable $topupDataTable): mixed
    {
        return $topupDataTable->render('admin.topup.index');
    }

    public function approve(int $id)
    {

        $topup = Topup::find($id);

        if (! $topup) {
            return response()->json(['message' => 'Hóa đơn không tồn tại'], 404);
        }

        if ($topup->status !== Topup::PENDING) {
            return $this->error('Hóa đơn không ở trạng thái chờ duyệt');
        }

        $topup->update(['status' => Topup::SUCCESS]);
        $user = $topup->user;
        $user->balance += $topup->amount;

        $refferrer_id = Affiliate::query()
            ->where('user_id', '=', $user->id)
            ->value('referrer_id');

        $refferrer = User::find($refferrer_id);

        if ($refferrer) {
            $commission = $topup->amount * $refferrer->commission / 100;
            $refferrer->balance += $commission;
            $refferrer->save();
            $this->balance->logBalance($refferrer->id, $commission, 'commission');

        }
        if ($user) {
            $user->save();
            $this->balance->logBalance($user->id, $topup->amount, 'plus');
        }

        return $this->success('Duyệt hóa đơn thành công');
    }

    public function cancel(int $id)
    {
        $topup = Topup::find($id);

        if (! $topup) {
            return response()->json(['message' => 'Hóa đơn không tồn tại'], 404);
        }

        if ($topup->status !== Topup::PENDING) {
            return $this->error('Hóa đơn không ở trạng thái có thể hủy');
        }
        $user = $topup->user;

        $topup->update(['status' => Topup::CANCEL]);

        return $this->success(' Hủy hóa đơn thành công');
    }
}
