<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_deposit_history',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatableDepositHistory;
        $(document).ready(function() {
            DatatableDepositHistory = window.LaravelDataTables["datatable_deposit_history"];
        });
    </script>
    <script>
        $(document).ready(function(){
            $("#model_export_deposit_history").click(function(){
                window.location.href = "{{ url('deposit-history/export') }}";
            });
        });
    </script>
@endpush
