<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accounts', function (Blueprint $table) {
            $table->id();
            $table->string('number')->unique();
            $table->string('name');
            $table->string('username');
            $table->string('password');
            $table->decimal('balance', 20, 2)->default(floatval(-1));
            $table->string('device_id');
            $table->text('api')->nullable();
            $table->integer('status'); //0 is inactive, 1 is idling, 2 is syncing, 3 is error
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accounts');
    }
};
