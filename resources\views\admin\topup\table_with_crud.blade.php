@include('admin.topup.table')
@push('scripts')
    <script type="text/javascript">
        $(document).on('click', '.approveButton', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: '{{__('Bạn có chắc chắn muốn duyệt nạp tiền này không?')}}',
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.post('topups/approve/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.title, response.data.message)
                                DatatableTopup.ajax.reload(null, false);
                            } else {
                                mess_error(response.data.title, response.data.message)
                            }
                        });
                }
            });
        });
        $(document).on('click', '.cancelButton', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: '{{__('Bạn có chắc chắn muốn hủy nạp tiền này không?')}}',
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.post('topups/cancel/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.title, response.data.message)
                                DatatableTopup.ajax.reload(null, false);
                            } else {
                                mess_error(response.data.title, response.data.message)
                            }
                        });
                }
            });
        });
    </script>
@endpush
