<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Proxy;
use App\Services\ProxyService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ProxyController extends Controller
{
    private ProxyService $proxyService;

    public function __construct(ProxyService $proxyService)
    {
        $this->proxyService = $proxyService;
    }

    public function getProxy(Request $request): \Illuminate\Http\JsonResponse
    {
        $query = Proxy::query()
            ->select('servers.ip_address', 'proxies.socks5_port', 'proxies.http_port', 'proxies.username', 'proxies.password', 'proxies.country', 'proxies.city', 'proxies.state', 'proxies.expired_at', 'proxies.reset_at', 'packages.reset_time', 'packages.expire_time')
            ->leftJoin('servers', 'proxies.server_id', '=', 'servers.id')
            ->leftJoin('rent_package_proxy', 'rent_package_proxy.proxy_id', '=', 'proxies.id')
            ->leftJoin('rent_packages', 'rent_packages.id', '=', 'rent_package_proxy.rent_package_id')
            ->leftJoin('packages', 'packages.id', '=', 'rent_packages.package_id')
            ->where('rent_packages.api_key', '=', $request->input('api_key'))
            ->where('rent_packages.status', '=', 1)
            ->first();
        if (! $query) {
            return response()->json(['success' => 'false', 'error' => 'Unauthorized'], 401);
        }
        if ($query['expire_time'] > 0) {
            $diff = Carbon::parse($query['expired_at'])->diff();
            $total_hours = $diff->days * 24 + $diff->h;
            $query['expired_at'] = Carbon::now()->gte($query['expired_at']) ? __('Đã hết hạn') : $total_hours.' giờ '.$diff->i.' phút '.$diff->s.' giây';
        } else {
            $query['expired_at'] = __('Không giới hạn');
        }
        if ($query['reset_time'] > 0) {
            $diff = Carbon::parse($query['reset_at'])->addMinutes($query['reset_time'])->diff();
            $total_hours = $diff->days * 24 + $diff->h;
            $query['reset_at'] = Carbon::now()->diffInMinutes($query['reset_at']) > $query['reset_time'] ? __('Được đổi Ip') : $total_hours.' giờ '.$diff->i.' phút '.$diff->s.' giây';
        } else {
            $query['reset_at'] = __('Không giới hạn');
        }
        unset($query['expire_time']);
        unset($query['reset_time']);

        return response()->json(['success' => true, 'data' => $query]);
    }

    public function resetProxy(Request $request): \Illuminate\Http\JsonResponse
    {
        $proxy = Proxy::query()->select('proxies.*')
            ->leftJoin('rent_package_proxy', 'rent_package_proxy.proxy_id', '=', 'proxies.id')
            ->leftJoin('rent_packages', 'rent_packages.id', '=', 'rent_package_proxy.rent_package_id')
            ->where('rent_packages.api_key', '=', $request->input('api_key'))
            ->where('rent_packages.status', '=', 1)
            ->first();
        if (! $proxy) {
            return response()->json(['success' => false, 'error' => 'Unauthorized'], 401);
        }

        return $this->proxyService->reset($proxy->id, 1);
    }

    public function statusProxy(Request $request): \Illuminate\Http\JsonResponse
    {
        $proxy = Proxy::query()
            ->select('proxies.*')
            ->leftJoin('rent_package_proxy', 'rent_package_proxy.proxy_id', '=', 'proxies.id')
            ->leftJoin('rent_packages', 'rent_packages.id', '=', 'rent_package_proxy.rent_package_id')
            ->where('rent_packages.api_key', '=', $request->input('api_key'))
            ->where('rent_packages.status', '=', 1)
            ->first();
        if (! $proxy) {
            return response()->json(['success' => false, 'error' => 'Unauthorized'], 401);
        }

        return $this->proxyService->status($proxy->id, 1);
    }
}
