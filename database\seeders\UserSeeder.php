<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('users')->insert(
            [
                [
                    'name' => 'Admin',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('123456'),
                    'type' => '0',
                    'created_by' => '0',
                ],
                [
                    'name' => 'User',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('123456'),
                    'type' => '1',
                    'created_by' => '0',
                ],
                [
                    'name' => 'User2',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('123456'),
                    'type' => '1',
                    'created_by' => '0',
                ],
                [
                    'name' => 'User3',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('123456'),
                    'type' => '1',
                    'created_by' => '0',
                ],
            ]
        );
    }
}
