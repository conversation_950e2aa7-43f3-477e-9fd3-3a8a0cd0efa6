<div class="modal fade" id="model_import_vpn" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Import Vpn') }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="form" id="form_import_vpn">
                    <div class="card-body">
                        @csrf
                        <div class="form-group">
                            <label for=name">{{ __('Tên file') }}<span style="color: red">*</span>:</label>
                            <input id="name" name="name" type="text" class="form-control form-control-solid"
                                   placeholder="{{ __('Tên') }}" />
                        </div>
                        <div class="form-group">
                            <label for="file">{{ __('Tên tệp') }}<span style="color: red">*</span>:</label>
                            <input id="file" name="file" type="file" class="form-control form-control-solid"
                                   placeholder="{{ __('Tệp') }}" />
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary mr-2">{{ __('Tải lên') }}</button>
                        <button type="reset" class="btn btn-secondary">{{ __('Nhập lại') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@push('scripts')
    <script type="text/javascript">
        $('#form_import_vpn').submit(function (e) {
            e.preventDefault();
            let form = $(this);
            axios({
                method: 'POST',
                url: 'vpn',
                data: form.serialize(),
            }).then((response) => {
                if (response.data.status) {
                    mess_success(response.data.title, response.data.message)
                    DatatableVpn.ajax.reload(null, false);
                } else {
                    mess_error(response.data.title, response.data.message)
                }
            });
        });
    </script>
@endpush
