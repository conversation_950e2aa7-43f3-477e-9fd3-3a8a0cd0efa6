<?php

namespace App\Models;

use App\Casts\DateTimeCast;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class RentPackage extends Model
{
    use HasFactory;

    const PENDING = 0;

    const ACTIVE = 1;

    const DISABLE = 2;

    /**
     * @var array
     */
    protected $fillable = [
        'package_id',
        'user_id',
        'api_key',
        'status',
        'expired_at',
    ];

    /**
     * @var array
     */
    protected $casts = [
        'package_id' => 'integer',
        'user_id' => 'integer',
        'status' => 'integer',
        'api_key' => 'string',
        'created_at' => DateTimeCast::class,
        'updated_at' => DateTimeCast::class,
        'expired_at' => DateTimeCast::class,
    ];

    public function proxy(): hasOneThrough
    {
        return $this->hasOneThrough(Proxy::class, RentPackageProxy::class, 'rent_package_id', 'id', 'id', 'proxy_id');
    }

    public function package(): HasOne
    {
        return $this->hasOne(Package::class, 'id', 'package_id');
    }

    public function user(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function order(): HasMany
    {
        return $this->hasMany(Order::class, 'package_id', 'id');
    }
}
