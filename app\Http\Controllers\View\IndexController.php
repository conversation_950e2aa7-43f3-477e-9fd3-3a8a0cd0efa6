<?php

namespace App\Http\Controllers\View;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\View\View;

class IndexController extends Controller
{
    public function show(): View
    {
        return view('home.index');
    }

    public function policy(): View
    {
        return view('home.policy', [
            'menu' => 'policy',
        ]);
    }
    public function term(): View
    {
        return view('home.term', [
            'menu' => 'term',
        ]);
    }
    public function downloadPC()
    {
        $downloadUrl = 'https://download.proxytn.com/ProxyTN%20VPN.msi';
        return redirect($downloadUrl);
    }

    public function downloadAndroid()
    {
        $downloadUrl = 'https://play.google.com/store/apps/details?id=com.tncompany.vpn&hl=vi';
        return redirect($downloadUrl);
    }

    public function downloadIOS()
    {
        $downloadUrl = 'https://testflight.apple.com/join/RyjFnKQq';
        return redirect($downloadUrl);
    }
}
