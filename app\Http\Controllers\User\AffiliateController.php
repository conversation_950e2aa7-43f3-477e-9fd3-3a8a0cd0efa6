<?php

namespace App\Http\Controllers\User;

use App\DataTables\AffiliateDataTable;
use App\DataTables\AffiliateTopupDataTable;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AffiliateController extends Controller
{
    public function index(AffiliateDataTable $affiliateDataTable, AffiliateTopupDataTable $affiliateTopupDataTable, Request $request)
    {
        if ($request->input('type') == 'topup') {
            return $affiliateTopupDataTable->render('user.affiliate.index');
        }

        return $affiliateDataTable->render('user.affiliate.index', [
            'affiliateTopupDataTable' => $affiliateTopupDataTable->html(),
        ]);
    }

    public function store(Request $request)
    {

        try {
            $newReferrerCode = $request->input('referrer_code');
            $query_find = User::query()->where('id', '=', Auth::id())->first();
            $existingUserWithCode = User::where('referrer_code', '=', $newReferrerCode)
                ->where('id', '!=', Auth::id())
                ->exists();
            if (! $existingUserWithCode) {
                if ($query_find->update([
                    'referrer_code' => $newReferrerCode,
                ])) {
                    return $this->success('Cập nhật mã giới thiệu thành công');
                }
            } else {
                return $this->error('Mã giới thiệu đã tồn tại.');
            }
        } catch (\Throwable $e) {
            return $this->error();
        }
    }
}
