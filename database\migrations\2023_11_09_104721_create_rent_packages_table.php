<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (! Schema::hasTable('rent_packages')) {
            Schema::create('rent_packages', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('package_id');
                $table->unsignedBigInteger('user_id');
                $table->string('api_key')->unique();
                $table->integer('status')->default(0);
                $table->timestamps();
                $table->timestamp('expired_at')->nullable();
            });
        }

        Schema::table('rent_packages', function (Blueprint $table) {
            $table->foreign('package_id')->references('id')->on('packages')
                ->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
