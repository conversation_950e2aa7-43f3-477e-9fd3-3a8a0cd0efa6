<?php

namespace App\DataTables;

use App\Repositories\BalanceHistoryRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class BalanceHistoryDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->editColumn('created_at', function ($query) {
                return Carbon::parse($query->created_at)->toDateTimeString();
            })
            ->editColumn('transaction_type', function ($query) {
                if ($query->transaction_type == 'plus') {
                    return '<span class="btn btn-success">'.__('Nạp tiền').'</span>';
                } elseif ($query->transaction_type == 'commission') {
                    return '<span class="btn btn-primary">'.__('Hoa hồng').'</span>';
                }

                return '<span class="btn btn-danger">'.__('Trừ tiền').'</span>';
            })->rawColumns(['transaction_type'])
            ->editColumn('amount', function ($query) {
                return number_format($query->amount);
            })
            ->editColumn('balance_after_transaction', function ($query) {
                return number_format($query->balance_after_transaction);
            });
    }

    private $temporaryUserId;

    public function setTemporaryUserId($userId)
    {
        $this->temporaryUserId = $userId;
    }

    public function query(): Builder
    {
        $query = (new BalanceHistoryRepository())->datatable()->latest();

        if ($this->temporaryUserId !== null) {
            $query->where('user_id', $this->temporaryUserId);
        }

        return $query;
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax('/balance-history', $this->getScript(), [], ['error' => 'function (err) { defaultOnError(err);}'])
            ->parameters([
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'user.email' => (new Column([
                'title' => __('Email'),
                'data' => 'user.email',
                'searchable' => true,
                'orderable' => false,
            ])),
            'user.name' => (new Column([
                'title' => __('Tên'),
                'data' => 'user.name',
                'searchable' => true,
                'orderable' => false,
            ])),
            'amount' => (new Column([
                'title' => __('Số tiền'),
                'data' => 'amount',
                'searchable' => true,
                'orderable' => false,
            ])),
            'transaction_type' => (new Column([
                'title' => __('Loại'),
                'data' => 'transaction_type',
                'searchable' => true,
                'orderable' => false,
            ])),
            'balance_after_transaction' => (new Column([
                'title' => __('Số dư'),
                'data' => 'balance_after_transaction',
                'searchable' => true,
                'orderable' => false,
            ])),
            'created_at' => (new Column([
                'title' => __('Thời gian'),
                'data' => 'created_at',
                'searchable' => true,
                'orderable' => false,
            ])),
        ];
    }

    protected function filename(): string
    {
        return __('balance_history').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
