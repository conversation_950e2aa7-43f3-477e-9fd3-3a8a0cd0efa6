
<div class="form-group">
    <label for="{{ $type }}name">{{ __('Tên gói') }}<span style="color: red">*</span>:</label>
    <input id="{{ $type }}name" name="name" type="text" class="form-control form-control-solid"
           placeholder="{{ __('Tên gói') }}" />
</div>
<div class="form-group">
    <label for="{{ $type }}type">{{ __('Loại') }}<span style="color: red">*</span>:</label>
    <select class="form-control form-control-solid" name="type" id="{{ $type }}type">
        <option value="proxy">Proxy</option>
        <option value="vpn">Vpn</option>
    </select>
</div>
<div class="form-group">
    <label for="{{ $type }}description">{{ __('Mô tả') }}<span style="color: red">*</span>:</label>
    <input id="{{ $type }}description" name="description" type="text" class="form-control form-control-solid"
           placeholder="{{ __('Mô tả') }}" />
</div>
<div class="form-group">
    <label for="{{ $type }}_price">{{ __('Giá') }}<span style="color: red">*</span>:</label>
    <input id="{{ $type }}price" name="price" type="number" min="0" class="form-control form-control-solid"
           placeholder="{{ __('Giá') }}" />
</div>
<div class="form-group">
    <label for="{{ $type }}duration">{{ __('Thời hạn gói') }}<span style="color: red">*</span>:</label>
    <input id="{{ $type }}duration" name="duration" type="number" min="0" class="form-control form-control-solid"
           placeholder="{{ __('Số ngày') }}" />
</div>

<div class="form-group" id="{{ $type }}reset_timeField" style="display: block;">
    <label for="{{ $type }}reset_time">{{ __('Thời gian xoay') }}:</label>
    <input id="{{ $type }}reset_time" name="reset_time" type="number" value="0" min="0" class="form-control form-control-solid"
           placeholder="{{ __('Số phút') }}" />
</div>
<div class="form-group" id="{{ $type }}expire_timeField" style="display: block;">
    <label for="{{ $type }}expire_time">{{ __('Thời gian dùng tối đa') }}:</label>
    <input id="{{ $type }}expire_time" name="expire_time" type="number" value="0" min="0" class="form-control form-control-solid"
           placeholder="{{ __('Số phút') }}" />
</div>

<script>
    function handleTypeChange() {
        var selectedType = document.getElementById('{{ $type }}type').value;
        var resetTimeField = document.getElementById('{{ $type }}reset_timeField');
        var expireTimeField = document.getElementById('{{ $type }}expire_timeField');
        var resetTimeValue = document.getElementById('{{ $type }}reset_time');
        var expireTimeValue = document.getElementById('{{ $type }}expire_time');

        if (selectedType === 'vpn') {
            resetTimeField.style.display = 'none';
            expireTimeField.style.display = 'none';
            resetTimeValue.value = '0';
            expireTimeValue.value = '0';
        } else {
            resetTimeField.style.display = 'block';
            expireTimeField.style.display = 'block';
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        handleTypeChange();
    });

    document.getElementById('{{ $type }}type').addEventListener('change', handleTypeChange);
</script>

