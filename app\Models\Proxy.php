<?php

namespace App\Models;

use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Proxy extends Model
{
    use Cachable, HasFactory;

    protected $table = 'proxies';

    /**
     * @var array
     */
    protected $fillable = [
        'server_id',
        'ip_address',
        'http_port',
        'socks5_port',
        'username',
        'password',
        'country',
        'city',
        'state',
        'zipcode',
        'isp',
        'reset_at',
        'expired_at',
    ];

    public function user(): HasOneThrough
    {
        return $this->hasOneThrough(User::class, UserProxy::class, 'proxy_id', 'id', 'id', 'user_id');
    }

    public function server(): HasOne
    {
        return $this->hasOne(Server::class, 'id', 'server_id');
    }

    public function rent_package(): HasOneThrough
    {
        return $this->hasOneThrough(RentPackage::class, RentPackageProxy::class, 'proxy_id', 'id', 'id', 'rent_package_id');
    }
}
