<?php

namespace App\Console\Commands;

use App\Jobs\UpdateAuthProxy;
use App\Models\Proxy;
use App\Models\RentPackage;
use App\Models\RentPackageProxy;
use App\Models\UserProxy;
use Illuminate\Console\Command;

class CheckExpiredPackage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-expired-package';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check packages that have expired and delete proxies or VPNs of those packages';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $expiredPackages = RentPackage::query()
            ->where('rent_packages.status', RentPackage::ACTIVE)
            ->whereDate('rent_packages.expired_at', '<', now())
            ->join('packages', 'packages.id', '=', 'rent_packages.package_id')
            ->select('rent_packages.*', 'packages.type as type')
            ->get();
        foreach ($expiredPackages as $package) {
            if ($package->type == 'proxy') {
                $expired_proxies = RentPackageProxy::query()->where('rent_package_id', '=', $package->id)->pluck('proxy_id');
                if (count($expired_proxies) > 0) {
                    $proxies = Proxy::query()->whereIn('id', $expired_proxies)->with('server:id,ip_address,api_key')->get();
                    dispatch(new UpdateAuthProxy($proxies->toArray()));
                    UserProxy::query()->whereIn('proxy_id', $expired_proxies)->delete();
                    RentPackageProxy::query()->whereIn('proxy_id', $expired_proxies)->delete();
                }
            }
            $package->update(['status' => RentPackage::DISABLE]);
        }
    }
}
