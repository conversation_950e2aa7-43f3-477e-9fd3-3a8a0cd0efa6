<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Account extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'number',
        'name',
        'username',
        'password',
        'balance',
        'device_id',
        'api',
        'status', //0 is inactive & un-sync, 1 is active & sync
    ];
}
