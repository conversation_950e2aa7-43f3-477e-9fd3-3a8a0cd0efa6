<?php

namespace App\DataTables;

use App\Models\Topup;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class TopupDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->addColumn('action', 'admin.topup.table_action')
            ->editColumn('created_at', function ($query) {
                return Carbon::parse($query->created_at)->toDateTimeString();
            })
            ->editColumn('amount', function ($query) {
                return number_format($query->amount);
            })
            ->editColumn('status', function ($query) {
                if ($query->status == 0) {
                    return '<span class="btn btn-warning">'.__('Đang xử lý').'</span>';
                }
                if ($query->status == 1) {
                    return '<span class="btn btn-success">'.__('Thành công').'</span>';
                }
                if ($query->status == 2) {
                    return '<span class="btn btn-danger">'.__('Quá hạn/Đã hủy').'</span> <span class="btn btn-outline-danger" data-toggle="modal" data-target="#model_check_again">'.__('Kiểm tra lại').'</span>';
                }

                return '<span class="btn btn-secondary">'.__('Từ chối').'</span>';
            })->rawColumns(['status', 'action']);
    }

    public function query(): Builder
    {
        $query = Topup::query()
            ->join('users', 'users.id', '=', 'topup.user_id')
            ->select('topup.*', 'users.name as username', 'users.email as email')
            ->when(Auth::user()->type === User::USER, function ($query) {
                $query->whereHas('user', function ($sub_query) {
                    $sub_query->where('id', Auth::id());
                });
            })
            ->orderBy('created_at', 'desc');

        return $query;
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax('/topup', $this->getScript(), [], ['error' => 'function (err) { defaultOnError(err);}'])
            ->addAction(['width' => '80px', 'responsivePriority' => -1, 'printable' => false, 'title' => __('Chức năng'), 'visible' => Auth::user()->type === User::ADMIN ? true : false])
            ->parameters([
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'code' => (new Column([
                'title' => __('Mã giao dịch'),
                'data' => 'code',
                'searchable' => true,
                'orderable' => false,
            ])),
            'email' => (new Column([
                'title' => __('Email'),
                'data' => 'email',
                'searchable' => true,
                'orderable' => false,
                'visible' => Auth::user()->type === User::ADMIN ? true : false,
            ])),
            'username' => (new Column([
                'title' => __('Khách hàng'),
                'data' => 'username',
                'searchable' => true,
                'orderable' => false,
            ])),
            'amount' => (new Column([
                'title' => __('Số tiền'),
                'data' => 'amount',
                'searchable' => true,
                'orderable' => false,
            ])),
            'status' => (new Column([
                'title' => __('Trạng thái'),
                'data' => 'status',
                'searchable' => true,
                'orderable' => false,
            ])),
            'created_at' => (new Column([
                'title' => __('Thời gian tạo'),
                'data' => 'created_at',
                'searchable' => true,
                'orderable' => false,
            ])),
        ];
    }

    protected function filename(): string
    {
        return __('Danh sách nạp tiền').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
