<?php

use App\Http\Controllers\Admin\InvoiceController;
use App\Http\Controllers\Admin\PackageController;
use App\Http\Controllers\Admin\ServerController;
use App\Http\Controllers\Admin\TopupController;
use App\Http\Controllers\Admin\TransactionController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\VpnController;
use App\Http\Controllers\Auth\ChangePasswordController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\MyProfileController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\View\IndexController;
use App\Http\Controllers\View\LanguageController;
use Illuminate\Support\Facades\Route;

//Language
Route::get('/language/{language}', [LanguageController::class, 'change']);
Route::get('/', [IndexController::class, 'show'])->name('home');
Route::get('/privacy-policy', [IndexController::class, 'policy'])->name('policy');
Route::get('/term-of-service', [IndexController::class, 'term'])->name('term');
Route::get('/download/pc', [IndexController::class, 'downloadPC'])->name('download.pc');
Route::get('/download/android', [IndexController::class, 'downloadAndroid'])->name('download.android');
Route::get('/download/ios', [IndexController::class, 'downloadIOS'])->name('download.ios');
//Auth
Route::get('/login', [LoginController::class, 'index'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/forgot-pass', [ForgotPasswordController::class, 'forgot_pass']);
Route::post('/reset-pass', [ResetPasswordController::class, 'reset_pass']);
Route::get('/register', [RegisterController::class, 'index'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);
Route::post('/register', [RegisterController::class, 'register']);

Route::group(['middleware' => ['auth']], function () {
    //Auth
    Route::get('/logout', [LoginController::class, 'logout']);

    //User
    Route::get('/profile', [MyProfileController::class, 'index']);
    Route::put('/profile', [MyProfileController::class, 'update']);
    Route::get('/change-pass', [ChangePasswordController::class, 'index']);
    Route::put('/change-pass', [ChangePasswordController::class, 'update']);

    //Vpn
    Route::group(['prefix' => 'proxies'], function () {
        Route::get('/export', [\App\Http\Controllers\User\PackageController::class, 'export']);
        Route::get('/{id}/reset', [\App\Http\Controllers\User\PackageController::class, 'reset']);
        Route::get('/{id}/status', [\App\Http\Controllers\User\PackageController::class, 'status']);
        Route::get('/{id}/reset-api-key', [\App\Http\Controllers\User\PackageController::class, 'reset_api_key']);
        Route::get('/{id}', [\App\Http\Controllers\User\PackageController::class, 'show']);
    });

    //Vpn
    Route::group(['prefix' => 'vpn'], function () {
        Route::get('/', [\App\Http\Controllers\User\VpnController::class, 'index'])->name('vpn.index');
        Route::get('/extend/{id}', [\App\Http\Controllers\User\VpnController::class, 'extend']);
    });

    //Balance history
    Route::group(['prefix' => 'balance-history'], function () {
        Route::get('/', [\App\Http\Controllers\User\BalanceHistoryController::class, 'index'])->name('balance_history.index');
        Route::get('/export', [\App\Http\Controllers\User\BalanceHistoryController::class, 'export']);
    });

    //Topup
    Route::group(['prefix' => 'topup'], function () {
        Route::get('/', [\App\Http\Controllers\User\TopupController::class, 'index'])->name('topup.index');
        Route::post('/', [\App\Http\Controllers\User\TopupController::class, 'create'])->name('user.topup.create');
        Route::post('/{id}', [\App\Http\Controllers\User\TopupController::class, 'cancel'])->name('user.topup.cancel');
    });

    //Deposit history
    Route::group(['prefix' => 'deposit-history'], function () {
        Route::get('/', [\App\Http\Controllers\User\DepositHistoryController::class, 'index'])->name('deposit_history.index');
        Route::get('/export', [\App\Http\Controllers\User\DepositHistoryController::class, 'export']);
    });

    //Invoice
    Route::group(['prefix' => 'invoices'], function () {
        Route::get('/', [\App\Http\Controllers\User\InvoiceController::class, 'index'])->name('invoice.index');
        Route::get('/detail/{id}', [\App\Http\Controllers\User\InvoiceController::class, 'detail'])->name('invoice.detail');
        Route::put('/status/{id}', [\App\Http\Controllers\User\InvoiceController::class, 'status'])->name('invoice.status');
        Route::put('/block-status/{id}', [\App\Http\Controllers\User\InvoiceController::class, 'block'])->name('invoice.block');
        Route::delete('{id}', [\App\Http\Controllers\User\InvoiceController::class, 'destroy'])->name('invoice.destroy');
    });

    //Package
    Route::group(['prefix' => 'proxy'], function () {
        Route::get('/', [\App\Http\Controllers\User\PackageController::class, 'index'])->name('proxy.index');
        Route::get('{id}', [\App\Http\Controllers\User\PackageController::class, 'show'])->name('proxy.show');
        Route::get('/view/{id}', [\App\Http\Controllers\User\PackageController::class, 'view']);
        Route::get('/extend/{id}', [\App\Http\Controllers\User\PackageController::class, 'extend']);
        Route::delete('{id}', [\App\Http\Controllers\User\PackageController::class, 'destroy'])->name('package.destroy');

    });

    Route::group(['prefix' => 'shop'], function () {
        Route::get('/', [\App\Http\Controllers\User\ShopController::class, 'index'])->name('shop.index');
        Route::post('/buy/{id}', [\App\Http\Controllers\User\ShopController::class, 'buyPackage']);
    });

    Route::group(['prefix' => 'api-document'], function () {
        Route::get('/', [\App\Http\Controllers\User\ApiDocumentController::class, 'index'])->name('api_document.index');
    });
    Route::group(['prefix' => 'affiliate'], function () {
        Route::get('/', [\App\Http\Controllers\User\AffiliateController::class, 'index'])->name('affiliate.index');
        Route::post('/save-referrer-code', [\App\Http\Controllers\User\AffiliateController::class, 'store'])->name('save.referrer.code');
    });

    Route::group(['prefix' => 'admin', 'middleware' => ['auth', 'is_admin']], function () {
        //User
        Route::get('/profile', [MyProfileController::class, 'index']);
        Route::put('/profile', [MyProfileController::class, 'update']);
        Route::get('/change-pass', [ChangePasswordController::class, 'index']);
        Route::put('/change-pass', [ChangePasswordController::class, 'update']);

        //User
        Route::group(['prefix' => 'users'], function () {
            Route::get('/', [UserController::class, 'index'])->name('admin.user.index');
            Route::put('/status/{id}', [UserController::class, 'status'])->name('admin.user.status');
            Route::put('/role/{id}', [UserController::class, 'role'])->name('admin.user.role');
            Route::delete('{id}', [UserController::class, 'destroy'])->name('admin.user.destroy');
            Route::get('/view/{id}', [UserController::class, 'show'])->name('admin.user.show');
            Route::post('/commission/{id}', [UserController::class, 'update'])->name('admin.user.update');
        });

        //Proxy Package
        Route::group(['prefix' => 'package'], function () {
            Route::get('/', [PackageController::class, 'index'])->name('admin.package.index');
            Route::get('{id}', [PackageController::class, 'show'])->name('admin.package.show');
            Route::post('/', [PackageController::class, 'store'])->name('admin.package.store');
            Route::put('{id}', [PackageController::class, 'update'])->name('admin.package.update');
            Route::delete('{id}', [PackageController::class, 'destroy'])->name('admin.package.destroy');
        });

        //VPN
        Route::group(['prefix' => 'vpn'], function () {
            Route::get('/', [VpnController::class, 'index'])->name('admin.vpn.index');
            Route::get('{id}', [VpnController::class, 'show'])->name('admin.vpn.show');
            Route::post('/', [VpnController::class, 'store'])->name('admin.vpn.store');
            Route::put('{id}', [VpnController::class, 'update'])->name('admin.vpn.update');
            Route::delete('{id}', [VpnController::class, 'destroy'])->name('admin.vpn.destroy');
        });

        //Server
        Route::group(['prefix' => 'servers'], function () {
            Route::get('/', [ServerController::class, 'index'])->name('admin.server.index');
            Route::get('{id}', [ServerController::class, 'show'])->name('admin.server.show');
            Route::post('/', [ServerController::class, 'store'])->name('admin.server.store');
            Route::put('{id}', [ServerController::class, 'update'])->name('admin.server.update');
            Route::delete('{id}', [ServerController::class, 'destroy'])->name('admin.server.destroy');
        });

        //Balance history
        Route::group(['prefix' => 'balance-history'], function () {
            Route::get('/', [\App\Http\Controllers\Admin\BalanceHistoryController::class, 'index'])->name('admin.balance_history.index');
            Route::get('/export', [\App\Http\Controllers\Admin\BalanceHistoryController::class, 'export']);
        });

        //Invoice
        Route::group(['prefix' => 'invoices'], function () {
            Route::get('/', [InvoiceController::class, 'index'])->name('admin.invoice.index');
            Route::put('/status/{id}', [InvoiceController::class, 'status'])->name('admin.invoice.status');
            Route::get('/detail/{id}', [InvoiceController::class, 'detail'])->name('admin.invoice.detail');
            Route::put('/block-status/{id}', [InvoiceController::class, 'block'])->name('admin.invoice.block');
            Route::delete('{id}', [InvoiceController::class, 'destroy'])->name('admin.invoice.destroy');
        });

        //Transaction
        Route::group(['prefix' => 'transactions'], function () {
            Route::get('/', [TransactionController::class, 'index'])->name('admin.transaction.index');
            Route::get('/export', [TransactionController::class, 'export']);
            Route::post('/change-status', [TransactionController::class, 'changeStatus']);

        });

        //Topup
        Route::group(['prefix' => 'topups'], function () {
            Route::get('/', [TopupController::class, 'index'])->name('admin.topup.index');
            Route::post('approve/{id}', [TopupController::class, 'approve'])->name('admin.topup.approve');
            Route::post('cancel/{id}', [TopupController::class, 'cancel'])->name('admin.topup.cancel');
        });
    });
});
