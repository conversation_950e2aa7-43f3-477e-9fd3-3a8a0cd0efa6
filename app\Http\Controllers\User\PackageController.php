<?php

namespace App\Http\Controllers\User;

use App\DataTables\UserProxyDataTable;
use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\Package;
use App\Models\Proxy;
use App\Models\RentPackage;
use App\Models\RentPackageProxy;
use App\Models\User;
use App\Models\UserProxy;
use App\Services\BalanceService;
use App\Services\PackageService;
use App\Services\ProxyService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;

class PackageController extends Controller
{
    private ProxyService $proxyService;

    private BalanceService $balance;

    private PackageService $packageService;

    public function __construct(
        ProxyService $proxyService,
        BalanceService $balanceService,
        PackageService $packageService,
    ) {
        $this->proxyService = $proxyService;
        $this->balance = $balanceService;
        $this->packageService = $packageService;
    }

    public function index(UserProxyDataTable $userPackageDataTable)
    {
        $packages = Package::query()->select('name')->where('type', '=', 'proxy')->get();

        return $userPackageDataTable->render('user.package.index', ['packages' => $packages]);
    }

    public function reset(int $id): JsonResponse
    {
        return $this->proxyService->reset($id);
    }

    public function status(int $id): JsonResponse
    {
        return $this->proxyService->status($id);
    }

    public function reset_api_key(int $id): JsonResponse
    {
        $api_key = $this->packageService->get_api_key();
        $rent_package = RentPackage::find($id);
        if (! $rent_package) {
            return $this->error('Không tìm thấy gói thuê');
        }
        $rent_package->api_key = $api_key;
        $rent_package->save();

        return $this->success('Reset API key thành công');
    }

    public function export()
    {
        if (Auth::check()) {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setCellValue('A1', __('STT'));
            $sheet->setCellValue('B1', __('API Key'));
            $sheet->setCellValue('C1', __('Type'));
            $sheet->setCellValue('D1', __('IP address'));
            $sheet->setCellValue('E1', __('Http Port'));
            $sheet->setCellValue('F1', __('Socks5 Port'));
            $sheet->setCellValue('G1', __('Username'));
            $sheet->setCellValue('H1', __('Password'));
            $sheet->setCellValue('I1', __('Country'));
            $sheet->setCellValue('J1', __('City'));
            $sheet->setCellValue('K1', __('State'));
            $sheet->setCellValue('L1', __('Zipcode'));
            $sheet->setCellValue('M1', __('Isp'));
            $rowCount = 2;
            $stt = 1;
            $listProxy = Proxy::query()
                ->disableCache()
                ->whereHas('user', function ($query) {
                    $query->where('id', '=', Auth::id());
                })
                ->get();
            foreach ($listProxy as $key) {
                $sheet->setCellValue('A'.$rowCount, $stt);
                $sheet->setCellValue('B'.$rowCount, $key['api_key']);
                $sheet->setCellValue('C'.$rowCount, $key['type']);
                $sheet->setCellValue('D'.$rowCount, $key['ip_address']);
                $sheet->setCellValue('E'.$rowCount, $key['http_port']);
                $sheet->setCellValue('F'.$rowCount, $key['socks5_port']);
                $sheet->setCellValue('G'.$rowCount, $key['username']);
                $sheet->setCellValue('H'.$rowCount, $key['password']);
                $sheet->setCellValue('I'.$rowCount, $key['country']);
                $sheet->setCellValue('J'.$rowCount, $key['city']);
                $sheet->setCellValue('K'.$rowCount, $key['state']);
                $sheet->setCellValue('L'.$rowCount, $key['zipcode']);
                $sheet->setCellValue('M'.$rowCount, $key['Isp']);
                $rowCount++;
                $stt++;
            }
            $writer = new Csv($spreadsheet);
            $writer->setUseBOM(true);
            $writer->setOutputEncoding('UTF-8');
            $dt = Carbon::now()->format('d-m-Y');
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="'.__('Danh sách proxy').' - '.$dt.'.csv"');
            $writer->save('php://output');
        }
    }

    public function extend($id)
    {
        $user_id = Auth::id();
        try {
            $rent_package = RentPackage::findOrFail($id);
            $package = Package::findOrFail($rent_package->package_id);
            $user = User::findOrFail($user_id);
            if ($user->balance < $package->price) {
                return response()->json(['status' => false, 'title' => 'Lỗi', 'message' => 'Xin lỗi, tài khoản không đủ tiền']);
            }
            $rentProxies = Proxy::query()
                ->disableCache()
                ->whereDoesntHave('user')
                ->limit(1)
                ->get();
            $availableProxyCount = $rentProxies->count();
            if (($package->type == 'proxy' && $availableProxyCount >= 1)) {
                $expired = \Illuminate\Support\Carbon::parse($rent_package['expired_at']);
                if (Carbon::now()->lte($expired)) {
                    $expiredDate = $expired->addDays($package->duration);
                } else {
                    $expiredDate = now()->addDays($package->duration);
                    if ($package->type == 'proxy') {
                        $query = RentPackageProxy::query()->where('rent_package_id', '=', $rent_package->id)->exists();
                        if (! $query) {
                            foreach ($rentProxies as $proxy) {
                                RentPackageProxy::create([
                                    'rent_package_id' => $rent_package->id,
                                    'proxy_id' => $proxy->id,
                                ]);
                                UserProxy::create([
                                    'user_id' => $user_id,
                                    'proxy_id' => $proxy->id,
                                ]);
                                $this->proxyService->reset($proxy->id);
                            }
                        }
                    }
                }
                $rent_package->update(['expired_at' => $expiredDate, 'status' => 1]);
                $order = Order::create([
                    'rent_package_id' => $rent_package->id,
                    'amount' => $package->price,
                    'day' => $package->duration,
                    'type' => 1,
                    'status' => 1,
                ]);
                $currentDateTime = Carbon::now();
                $typeAbbreviation = strtoupper(substr($package->type, 0, 3));
                $invoiceCode = $typeAbbreviation.$currentDateTime->format('YmdHis');
                Invoice::create([
                    'order_id' => $order->id,
                    'payment_gateway' => 1,
                    'code' => $invoiceCode,
                ]);

                $user->balance -= $package->price;
                $user->save();

                $this->balance->logBalance(Auth::id(), $package->price, 'minus');

                return response()->json(['status' => true, 'title' => 'Thông báo', 'message' => 'Mua thành công']);
            } else {
                return response()->json(['status' => false, 'title' => 'Lỗi', 'message' => 'Xin lỗi, cửa hàng không đủ tài nguyên']);
            }
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'title' => 'Lỗi', 'message' => 'Đã xảy ra lỗi khi mua']);
        }
    }

    public function show(int $id): JsonResponse
    {
        $query = Proxy::query()
            ->select('proxies.*', 'servers.subdomain as subdomain', 'servers.ip_address as server_ip', 'rent_package_proxy.rent_package_id as rent_package_id')
            ->leftJoin('servers', 'proxies.server_id', '=', 'servers.id')
            ->leftJoin('rent_package_proxy', 'proxies.id', '=', 'rent_package_proxy.proxy_id')
            ->leftJoin('rent_packages', 'rent_package_proxy.rent_package_id', '=', 'rent_packages.id')
            ->leftJoin('packages', 'rent_packages.package_id', '=', 'packages.id')
            ->whereHas('user', function ($query) {
                $query->where('users.id', '=', Auth::id());
            })
            ->addSelect('packages.name as package_name')
            ->find($id);
        if ($query['expire_time'] > 0) {
            $diff = Carbon::parse($query['expired_at'])->diff();
            $total_hours = $diff->days * 24 + $diff->h;
            $query['expired_at'] = Carbon::now()->gte($query['expired_at']) ? __('Đã hết hạn') : $total_hours.' giờ '.$diff->i.' phút '.$diff->s.' giây';
        } else {
            $query['expired_at'] = __('Không giới hạn');
        }
        if ($query['reset_time'] > 0) {
            $diff = Carbon::parse($query['reset_at'])->addMinutes($query['reset_time'])->diff();
            $total_hours = $diff->days * 24 + $diff->h;
            $query['reset_at'] = Carbon::now()->diffInMinutes($query['reset_at']) > $query['reset_time'] ? __('Được đổi Ip') : $total_hours.' giờ '.$diff->i.' phút '.$diff->s.' giây';
        } else {
            $query['reset_at'] = __('Không giới hạn');
        }
        unset($query['expire_time']);
        unset($query['reset_time']);
        if ($query) {
            return $this->success(data: $query);
        }

        return $this->error();
    }

    public function destroy(int $id): JsonResponse
    {
        $query = RentPackage::query()->where('id', $id)->delete();
        if ($query) {

            return $this->success('Xoá hóa gói thuê thành công');
        }

        return $this->error();
    }
}
