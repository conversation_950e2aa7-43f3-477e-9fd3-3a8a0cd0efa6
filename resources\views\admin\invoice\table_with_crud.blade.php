@include('admin.invoice.table')
@push('scripts')
    <script type="text/javascript">
        $(document).on('click', '.destroy_invoice', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: '{{__('Bạn có chắc chắn muốn xoá proxy này không?')}}',
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.delete('invoices/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.title, response.data.message)
                                DatatableInvoice.ajax.reload(null, false);
                            } else {
                                mess_error(response.data.title, response.data.message)
                            }
                        });
                }
            });
        });
        $(document).on('click', '.view_detail2', function(e) {
            e.preventDefault();
            let code = $(this).data('id');
            window.location.href = '/admin/invoices/detail/' + code;
        });
    </script>
@endpush
