<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('account_id');
            $table->string('code')->unique();
            $table->decimal('amount', 20)->default(0);
            $table->string('type')->default('minus'); //minus or plus
            $table->string('from_name')->nullable();
            $table->string('from_account')->nullable();
            $table->string('from_bank')->nullable();
            $table->string('note')->nullable();
            $table->timestamp('received_at');
            $table->timestamps();
            $table->foreign('account_id')->references('id')->on('accounts')->restrictOnDelete()->restrictOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
