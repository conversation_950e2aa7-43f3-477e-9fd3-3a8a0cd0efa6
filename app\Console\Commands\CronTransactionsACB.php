<?php

namespace App\Console\Commands;

use App\Http\Controllers\AcbSystem;
use App\Models\Account;
use Illuminate\Console\Command;

class CronTransactionsACB extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:transactions-acb';

    protected $description = 'Fetch and store ACB bank transactions from API';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $account = Account::query()->first();
        if ($account->status == 1) {
            $system = new AcbSystem(1);
            if ($system->keep()) {
                $system->history();
            }
        }
    }
}
