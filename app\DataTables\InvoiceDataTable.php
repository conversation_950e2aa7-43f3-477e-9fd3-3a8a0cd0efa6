<?php

namespace App\DataTables;

use App\Models\Order;
use App\Repositories\InvoiceRepository;
use Illuminate\Database\Eloquent\Builder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class InvoiceDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->addColumn('action', 'user.invoice.table_action')
            ->editColumn('order.type', function ($query) {
                if ($query->order->type == Order::TRIAL) {
                    return __('Dùng thử');
                }
                if ($query->order->type == Order::RENT) {
                    return __('Thuê');
                }

                return __('<PERSON>ia hạn');
            })
            ->editColumn('order.status', function ($query) {
                if ($query->order->status == Order::PENDING) {
                    return '<span class="btn btn-warning">'.__('Đang xử lý').'</span>';
                }
                if ($query->order->status == Order::SUCCESS) {
                    return '<span class="btn btn-success">'.__('Thành công').'</span>';
                }
                if ($query->order->status == Order::CANCEL) {
                    return '<span class="btn btn-danger">'.__('Từ chối').'</span>';
                }

                return '<span class="btn btn-secondary">'.__('Thất bại').'</span>';
            })
            ->editColumn('order.amount', function ($query) {
                return number_format($query->order->amount);
            })
            ->editColumn('type', function ($query) {
                if ($query->status == 'IN') {
                    return '<span class="btn btn-success">'.__('Tiền vào').'</span>';
                }

                return '<span class="btn btn-danger">'.__('Tiền ra').'</span>';
            })->rawColumns(['type', 'order.status', 'action']);
    }

    private $temporaryUserId;

    public function setTemporaryUserId($userId)
    {
        $this->temporaryUserId = $userId;
    }

    public function query(): Builder
    {
        $query = (new InvoiceRepository())->invoice_datatable();
        if ($this->temporaryUserId !== null) {
            $query->join('orders', 'invoices.order_id', '=', 'orders.id')
                ->join('rent_packages', 'orders.rent_package_id', '=', 'rent_packages.id');
            $query->where('rent_packages.user_id', $this->temporaryUserId);
        }

        //user_id có trang bảng rent_packages, lối với bảng orders.rent_package_id và invoice.order_id
        return $query;
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax('/invoices', $this->getScript(), [], ['error' => 'function (err) { defaultOnError(err);}'])
            ->addAction(['width' => '80px', 'responsivePriority' => -1, 'printable' => false, 'title' => __('Chức năng')])
            ->parameters([
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'code' => (new Column([
                'title' => __('Mã hóa đơn'),
                'data' => 'code',
                'searchable' => true,
                'orderable' => false,
            ])),
            'order.rent_package.package.name' => (new Column([
                'title' => __('Tên gói'),
                'data' => 'order.rent_package.package.name',
                'searchable' => true,
                'orderable' => false,
            ])),
            'order.rent_package.user.name' => (new Column([
                'title' => __('Người dùng'),
                'data' => 'order.rent_package.user.name',
                'searchable' => true,
                'orderable' => false,
            ])),
            'order.created_at' => (new Column([
                'title' => __('Ngày tạo'),
                'data' => 'order.created_at',
                'searchable' => true,
                'orderable' => false,
            ])),
            'order.type' => (new Column([
                'title' => __('Loại'),
                'data' => 'order.type',
                'searchable' => true,
                'orderable' => false,
            ])),
            'order.amount' => (new Column([
                'title' => __('Tổng tiền'),
                'data' => 'order.amount',
                'searchable' => true,
                'orderable' => false,
            ])),
            'order.status' => (new Column([
                'title' => __('Trạng thái'),
                'data' => 'order.status',
                'searchable' => true,
                'orderable' => false,
            ])),
        ];
    }

    protected function filename(): string
    {
        return __('invoice').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
