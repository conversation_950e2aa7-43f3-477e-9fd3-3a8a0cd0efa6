<?php

namespace App\Repositories;

use App\Models\Server;
use Illuminate\Database\Eloquent\Builder;

class ServerRepository extends BaseRepository
{
    public function model(): string
    {
        return Server::class;
    }

    public function datatable(): Builder
    {
        return $this->query()
            ->select('id', 'ip_address', 'state', 'city')
            ->withCount('proxies');
    }
}
