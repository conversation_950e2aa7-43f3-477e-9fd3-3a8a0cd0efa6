@include('admin.vpn.table')
@include('admin.vpn.validate')
@include('admin.vpn.modal_create')
@include('admin.vpn.modal_update')
@include('admin.vpn.modal_import')
@push('scripts')
    <script type="text/javascript">
        $(document).on('click', '.destroy_vpn', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: '{{__('Bạn có chắc chắn muốn xoá vpn này không?')}}',
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.delete('vpn/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.title, response.data.message)
                                DatatableVpn.ajax.reload(null, false);
                            } else {
                                mess_error(response.data.title, response.data.message)
                            }
                        });
                }
            });
        });
    </script>
@endpush
