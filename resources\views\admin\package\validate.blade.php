@push('scripts')
    <script type="text/javascript">
        let field = {
            name: {
                validators: {
                    notEmpty: {
                        message: '{{__('Vui lòng không để trống mục này')}}'
                    },
                    stringLength: {
                        max: 255,
                        message: '{{__('Vui lòng không điền quá 255 kí tự')}}'
                    },
                }
            },
            description: {
                validators: {
                    notEmpty: {
                        message: '{{__('Vui lòng không để trống mục này')}}'
                    },
                    stringLength: {
                        max: 255,
                        message: '{{__('Vui lòng không điền quá 255 kí tự')}}'
                    },
                }
            },
            price: {
                validators: {
                    notEmpty: {
                        message: '{{__('Vui lòng không để trống mục này')}}'
                    },
                    stringLength: {
                        max: 255,
                        message: '{{__('Vui lòng không điền quá 255 kí tự')}}'
                    },
                }
            },
            duration: {
                validators: {
                    notEmpty: {
                        message: '{{__('Vui lòng không để trống mục này')}}'
                    },
                    stringLength: {
                        max: 255,
                        message: '{{__('Vui lòng không điền quá 255 kí tự')}}'
                    },
                }
            },
            quantity: {
                validators: {
                    notEmpty: {
                        message: '{{__('Vui lòng không để trống mục này')}}'
                    },
                    stringLength: {
                        max: 255,
                        message: '{{__('Vui lòng không điền quá 255 kí tự')}}'
                    },
                }
            },
        }
    </script>
@endpush
