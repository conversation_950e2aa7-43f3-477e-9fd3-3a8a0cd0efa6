<div class="modal fade" id="model_update_commission" tabindex="-1" role="dialog" aria-labelledby="labelModelEdit" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-s" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Cập nhật hoa hồng') }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="form" id="form_update_commission">
                    <div class="card-body">
                        @csrf
                        @method('POST')
                        <div class="form-group">
                            <label for="commission">{{ __('Phần trăm hoa hồng') }}<span style="color: red">*</span>:</label>
                            <input id="update_commission" name="commission" type="number" min="0" max="100" class="form-control form-control-solid"
                                   placeholder="{{ __('Phần trăm hoa hồng') }}" />
                            <label for="password">{{ __('Mật khẩu') }}:</label>
                            <input id="update_password" name="password" type="text"  class="form-control form-control-solid"
                                   placeholder="{{ __('Để trống nếu không thay đổi') }}" />
                            <label for="balance">{{ __('Số dư') }}:</label>
                            <input id="update_balance" name="balance" type="number"  class="form-control form-control-solid"
                                   placeholder="{{ __('Số dư') }}" />
                        </div>
                        <input type="hidden" id="update_id">
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary mr-2">{{ __('Cập nhật') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@push('scripts')
    <script type="text/javascript">
        $(document).ready(function () {
            $('#form_update_commission').submit(function (e) {
                e.preventDefault();
                let form = $(this);
                let id = $('#update_id').val();
                console.log(id);
                axios({
                    method: 'POST',
                    url: '{{ route('admin.user.index') }}/commission/' + id,
                    data: form.serialize(),
                }).then((response) => {
                    if (response.data.status) {
                        mess_success(response.data.title, response.data.message)
                        DatatableUser.ajax.reload(null, false);
                        $('#model_update_commission').closest('.modal').modal('toggle');
                    } else {
                        mess_error(response.data.title, response.data.message)
                    }
                });
            });
        });
        $(document).on('click', '.view_user2', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            axios.get('users/view/' + id)
                .then((response) => {
                    if (response.data.status) {
                        let user = response.data.data;
                        $('#update_id').val(user.id);
                        $('#update_commission').val(user.commission);
                        $('#update_balance').val(user.balance);
                    } else {
                        mess_error(response.data.title, response.data.message)
                    }
                });
        });
    </script>
@endpush
