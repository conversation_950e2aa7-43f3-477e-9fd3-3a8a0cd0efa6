<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Repositories\UserRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function login(LoginRequest $request): JsonResponse
    {
        $input = $request->all();
        if (! Auth::attempt(['email' => $input['email'], 'password' => $input['password']])) {
            $query_exits = $this->userRepository->exitsColumn('email', $input['email']);

            return $this->error($query_exits ? 'The password you entered is incorrect' : 'Email does not exist in the system');
        }
        $user = Auth::user()->only(['email', 'phone', 'name', 'status']);
        if ($user['status'] != 0) {
            return $this->error('Your account has been locked');
        }

        return $this->success(data: [
            'user' => $user,
            'type_token' => 'Bearer',
            'access_token' => Auth::user()->createToken('authToken')->plainTextToken,
        ]);
    }

    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return $this->success();
    }
}
