<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Vpn;
use Illuminate\Http\Request;

class LocationController extends Controller
{
    public function getCountry()
    {
        $country['country'] = Vpn::distinct()->pluck('country');

        return $this->success(data: $country);
    }

    public function getState(Request $request)
    {
        $state['state'] = Vpn::where('country', $request->country)
            ->distinct()->pluck('state');

        return $this->success(data: $state);
    }

    public function getCity(Request $request)
    {
        try {
            $city['city'] = Vpn::where('country', $request->country)
                ->where('state', $request->state)->distinct()->pluck('city');

            return $this->success(data: $city);
        } catch (\Exception $e) {
            return $this->error();
        }
    }
}
