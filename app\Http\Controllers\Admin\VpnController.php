<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\VpnDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Create\CreateVpnRequest;
use App\Http\Requests\Admin\Update\UpdateVpnRequest;
use App\Repositories\VpnRepository;
use Illuminate\Http\JsonResponse;

class VpnController extends Controller
{
    private VpnRepository $vpn;

    public function __construct(
        VpnRepository $VpnRepository,
    ) {
        $this->vpn = $VpnRepository;
    }

    public function index(VpnDataTable $vpnDataTable)
    {
        return $vpnDataTable->render('admin.vpn.index');
    }

    public function store(CreateVpnRequest $request): JsonResponse
    {
        $input = $request->all();
        $query = $this->vpn->create($input);
        if ($query) {

            return $this->success('Thêm vpn thành công');
        }

        return $this->error();
    }

    public function show(int $id): JsonResponse
    {
        $response = $this->vpn->find($id);
        if ($response) {
            return $this->success(data: $response);
        }

        return $this->error();
    }

    public function update(int $id, UpdateVpnRequest $request): JsonResponse
    {
        $input = $request->all();
        $query = $this->vpn->find($id);
        if ($query) {
            $query->update($input);

            return $this->success('Cập nhật vpn thành công');
        }

        return $this->error();
    }

    public function destroy(int $id): JsonResponse
    {
        $query = $this->vpn->delete($id);
        if ($query) {

            return $this->success('Xoá vpn thành công');
        }

        return $this->error();
    }
}
