<div class='dropdown'>
    <span class='dropdown cursor-pointer' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'>
        <i class='fa fa-ellipsis-h' aria-hidden='true'></i>
    </span>
    <ul class='dropdown-menu cursor-pointer'>
        @if($status == 1)
        <li data-id='{{$proxy_id}}' style='padding: 5px 10px;' class='resetButton dropdown-item' title="{{__('Reset IP')}}">
            <i class="las la-sync-alt"></i>{{ __('Reset IP') }}
        </li>
        <li data-id='{{$id}}' style='padding: 5px 10px;' class='resetApiKeyButton dropdown-item' title="{{__('Reset Api Key')}}">
            <i class="las la-redo-alt"></i>{{ __('Reset Api Key') }}
        </li>
        <li data-id='{{$proxy_id}}' data-toggle="modal" data-target="#model_proxy_detail" style='padding: 5px 10px;' class='view_proxy dropdown-item' title="{{__('Chi tiết')}}">
            <i class="las la-box"></i>{{ __('Chi tiết') }}
        </li>
        <li data-id='{{$proxy_id}}' style='padding: 5px 10px;' class='checkButton dropdown-item' title="{{__('Kiểm tra')}}">
            <i class="las la-check"></i>{{ __('Kiểm tra') }}
        </li>
        @endif
        <li data-id='{{$id}}' style='padding: 5px 10px;' class='extendButton dropdown-item' title="{{__('Gia hạn')}}">
            <i class="las la-newspaper"></i>{{ __('Gia hạn') }}
        </li>
        @if($status == 2)
        <li data-id='{{$id}}' style='padding: 5px 10px;' class='destroyButton dropdown-item' title="{{__('Xóa')}}">
            <i class="las la-trash-alt"></i>{{ __('Xóa') }}
        </li>
        @endif
    </ul>
</div>
@include('user.package.modal_detail')


