<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_invoice',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatableInvoice;
        $(document).ready(function() {
            DatatableInvoice = window.LaravelDataTables["datatable_invoice"];
        });
    </script>
@endpush
