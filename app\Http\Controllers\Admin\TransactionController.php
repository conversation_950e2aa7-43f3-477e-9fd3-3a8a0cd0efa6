<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\TransactionDataTable;
use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Models\Transaction;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;

class TransactionController extends Controller
{
    public function index(TransactionDataTable $transactionDataTable): mixed
    {
        $account = Account::query()->first();
        $status = $account->status;
        return $transactionDataTable->render('admin.transaction.index',[
            'status' => $status
        ]);
    }
    public function changeStatus()
    {
        $latestAccount = Account::query()->first();
        //0 is off, 1 is on
        if ($latestAccount) {
            if ($latestAccount->status == 0) {
                $latestAccount->status = 1;
            } elseif ($latestAccount->status == 1) {
                $latestAccount->status = 0;
            }
            $latestAccount->save();
            return $this->success('Thay đổi trạng thái thành công');
        } else {
            return $this->error();
        }
    }


    public function export()
    {
        if (Auth::check()) {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setCellValue('A1', __('STT'));
            $sheet->setCellValue('B1', __('Ngân hàng'));
            $sheet->setCellValue('C1', __('Mã GD'));
            $sheet->setCellValue('D1', __('Số tiền'));
            $sheet->setCellValue('E1', __('Loại'));
            $sheet->setCellValue('F1', __('Mô tả'));
            $sheet->setCellValue('G1', __('Thời gian'));
            $rowCount = 2;
            foreach (Transaction::all() as $key) {
                $sheet->setCellValue('A'.$rowCount, $key['id']);
                $sheet->setCellValue('B'.$rowCount, $key['bank.name']);
                $sheet->setCellValue('C'.$rowCount, $key['code']);
                $sheet->setCellValue('D'.$rowCount, $key['amount']);
                $sheet->setCellValue('E'.$rowCount, $key['type']);
                $sheet->setCellValue('F'.$rowCount, $key['description']);
                $sheet->setCellValue('G'.$rowCount, $key['created_at']);
                $rowCount++;
            }
            $writer = new Csv($spreadsheet);
            $writer->setUseBOM(true);
            $writer->setOutputEncoding('UTF-8');
            $dt = Carbon::now()->format('d-m-Y');
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="'.__('Lịch sử giao dịch ngân hàng').' - '.$dt.'.csv"');
            $writer->save('php://output');

        }
    }
}
