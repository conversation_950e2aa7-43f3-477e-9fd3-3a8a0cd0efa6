@push('scripts')
    <script type="text/javascript">
        $('#checkButton').on('click', function() {
            location.reload(true);
        });
        $(document).ready(function () {
            $("#cancelButton").click(function () {
                var topupId = $(this).data('id');
                $.ajax({
                    url: '/topup/' + topupId,
                    type: 'POST',
                    data: { _method: 'POST', _token: '{{ csrf_token() }}' },
                    success: function (response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Thành công',
                            text: 'Hủy nạp tiền thành công!.',
                        }).then(function () {
                            location.reload();
                        });                    },
                    error: function (error) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Thất bại',
                            text: 'Có lỗi xảy ra! Vui lòng thử lại.',
                        }).then(function () {
                            location.reload();
                        });
                    }
                });
            });
        });
    </script>
@endpush
