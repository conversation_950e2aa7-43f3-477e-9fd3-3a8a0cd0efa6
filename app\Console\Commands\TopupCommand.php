<?php

namespace App\Console\Commands;

use App\Models\Topup;
use App\Models\Transaction;
use App\Services\BalanceService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class TopupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:topup-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check bank for auto topup';

    protected $balance;

    public function __construct(BalanceService $balance)
    {
        parent::__construct();
        $this->balance = $balance;
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $transactions = Transaction::whereNotNull('note')
            ->where('created_at', '>=', Carbon::now()->subMonth())
            ->get();

        $transactionsToProcess = [];

        foreach ($transactions as $transaction) {
            $topup = Topup::where('status', '!=', Topup::SUCCESS)
                ->where('code', trim($transaction->note))
                ->latest()
                ->first();

            if ($topup) {
                $transactionsToProcess[] = [
                    'topup' => $topup,
                    'transaction' => $transaction,
                ];
            }
        }

        foreach ($transactionsToProcess as $item) {
            $topup = $item['topup'];
            $transaction = $item['transaction'];

            $topup->update(['status' => Topup::SUCCESS]);

            $user = $topup->user;
            $user->balance += $transaction->amount;
            $user->save();

            $this->balance->logBalance($user->id, $transaction->amount, 'plus');
        }
    }
}
