<?php

namespace App\DataTables;

use App\Models\RentPackage;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class UserProxyDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->addColumn('action', 'user.package.table_action')
            ->editColumn('price', function ($query) {
                return number_format($query->price);
            })
            ->editColumn('updated_at', function ($query) {
                return Carbon::parse($query->updated_at)->format('Y-m-d H:i:s');
            })
            ->editColumn('status', function ($query) {
                if ($query->status == RentPackage::PENDING) {
                    return '<span class="btn btn-warning">'.__('Đang xử lý').'</span>';
                } elseif ($query->status == RentPackage::ACTIVE) {
                    return '<span class="btn btn-success">'.__('Còn hạn').'</span>';
                }

                return '<span class="btn btn-danger">'.__('Hết hạn').'</span>';
            })
            ->rawColumns(['status', 'action']);
    }

    public function query(): Builder
    {
        return RentPackage::query()
            ->join('packages', 'packages.id', '=', 'rent_packages.package_id')
            ->leftJoin('rent_package_proxy', 'rent_package_proxy.rent_package_id', '=', 'rent_packages.id')
            ->leftJoin('proxies', 'proxies.id', '=', 'rent_package_proxy.proxy_id')
            ->whereHas('user', function ($query) {
                $query->where('id', '=', Auth::id());
            })
            ->where('packages.type', '=', 'proxy')
            ->select('rent_packages.*', 'proxies.id as proxy_id', 'proxies.ip_address as proxies_ip', 'packages.name as packages.name')
            ->orderBy('rent_packages.created_at', 'desc');
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->addAction(['width' => '80px', 'responsivePriority' => -1, 'printable' => false, 'title' => __('Chức năng')])
            ->parameters([
                'dom' => "<'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7 dataTables_pager'lp>>",
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'api_key' => (new Column([
                'title' => __('Api Key'),
                'data' => 'api_key',
                'searchable' => true,
                'orderable' => false,
            ])),
            'packages.name' => (new Column([
                'title' => __('Tên gói'),
                'data' => 'packages.name',
                'searchable' => true,
                'orderable' => false,
            ])),
            'proxies_ip' => (new Column([
                'title' => __('Địa chỉ ip'),
                'data' => 'proxies_ip',
                'searchable' => true,
                'orderable' => false,
            ])),
            'updated_at' => (new Column([
                'title' => __('Ngày mua'),
                'data' => 'updated_at',
                'searchable' => true,
                'orderable' => false,
            ])),
            'expired_at' => (new Column([
                'title' => __('Hạn sử dụng'),
                'data' => 'expired_at',
                'searchable' => true,
                'orderable' => false,
            ])),
            'status' => (new Column([
                'title' => __('Trạng thái'),
                'data' => 'status',
                'searchable' => true,
                'orderable' => false,
            ])),
        ];
    }

    protected function filename(): string
    {
        return __('Danh sách proxy').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
