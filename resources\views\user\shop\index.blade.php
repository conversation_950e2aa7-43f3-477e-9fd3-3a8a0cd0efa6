@extends('layout.user')
@section('content')
    <div class="card card-custom">
        @include('user.shop.header')
        <div class="card-body">
            <div id="apiOptions" class="row">
                <div class="col-6">
                    <button  onclick="togglePackage('proxy')" data-toggle="collapse" data-target="#list_proxy" class="btn btn-primary w-100" id="proxy2">Proxy</button>
                </div>
                <div class="col-6">
                    <button onclick="togglePackage('vpn')" data-toggle="collapse" data-target="#list_vpn" class="btn btn-primary w-100" id="vpn2" style="opacity: 0.5">VPN</button>
                </div>
            </div>
            <div style="margin-top: 10px; ">
                <div id="list_proxy" class="collapse m-3 row" style="display: flex">
                    @if (isset($proxyPackages) && count($proxyPackages) > 0)
                        @foreach($proxyPackages as $package)
                            <div class="col-lg-4 col-md-6 col-12 mt-1">
                                <div class="card">
                                    <div class="card-header h2 text-center">Tên gói: {{ $package->name }}</div>
                                    <div class="card-body">
                                        <p>✔ Nội dung: {{ $package->description }}</p>
                                        <p>✔ Số lượng: {{ $package->quantity }} cái</p>
                                        <p>✔ Thời hạn: {{ $package->duration }} ngày</p>
                                        @if($package->reset_time == 0)
                                            <p>✔ Không giới hạn thời giay xoay</p>
                                        @else
                                            <p>✔ Giới hạn thời giay xoay: {{ $package->reset_time }} phút</p>
                                        @endif
                                        <p class="text-gray-900 bold text-center" style="font-size: 40px;">{{ number_format($package->price) }} đ</p>
                                        <div class="row">
                                            <button  onclick="buyPackageOnClick({{ $package->id }})" class="buy_package btn btn-primary text-center text-center px-4 py-3 mx-auto" style="border-radius: 9999px;"> MUA NGAY </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <p>Hiện chưa có gói proxy nào! Vui lòng quay lại sau!</p>
                    @endif
                </div>
            </div>

            <div >
                <div id="list_vpn" class="collapse m-3 row" style="display: none">
                    @if (isset($vpnPackages) && count($vpnPackages) > 0)
                        @foreach($vpnPackages as $package)
                            <div class="col-lg-4 col-md-6 col-12 mt-1">
                                <div class="card">
                                    <div class="card-header h2 text-center">Tên gói: {{ $package->name }}</div>
                                    <div class="card-body">
                                        <p>✔ Nội dung: {{ $package->description }}</p>
                                        <p>✔ Số lượng: {{ $package->quantity }} cái</p>
                                        <p>✔ Thời hạn: {{ $package->duration }} ngày</p>
                                        <p class="text-gray-900 bold text-center" style="font-size: 40px;">{{ number_format($package->price) }} đ</p>
                                        <div class="row">
                                            <button onclick="buyPackageOnClick({{ $package->id }})" class="buy_package btn btn-primary text-center text-center px-4 py-3 mx-auto" style="border-radius: 9999px;"> MUA NGAY </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <p>Hiện chưa có gói vpn nào! Vui lòng quay lại sau!</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <script>
        function togglePackage(type) {
            if (type === 'proxy') {
                document.getElementById('list_proxy').style.display = 'flex';
                document.getElementById('list_vpn').style.display = 'none';
                document.getElementById('proxy2').style.opacity = '1';
                document.getElementById('vpn2').style.opacity = '0.5';
            } else if (type === 'vpn') {
                document.getElementById('list_proxy').style.display = 'none';
                document.getElementById('list_vpn').style.display = 'flex';
                document.getElementById('proxy2').style.opacity = '0.5';
                document.getElementById('vpn2').style.opacity = '1';

            }
        }
        function buyPackageOnClick(id) {
            Swal.fire({
                title: 'Xác nhận',
                text: 'Bạn có chắc chắn muốn mua gói proxy này không?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Có',
                cancelButtonText: 'Không',
            }).then((result) => {
                if (result.isConfirmed) {
                    axios.post('shop/buy/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Thành công',
                                    text: 'Mua gói thành công!',
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                mess_error(response.data.title, response.data.message);
                            }
                        });
                }
            });
        }


    </script>
@endsection
