<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Server extends Model
{
    use HasFactory;

    protected $table = 'servers';

    /**
     * @var array
     */
    protected $fillable = [
        'ip_address',
        'subdomain',
        'api_key',
        'city',
        'state',
    ];

    public function proxies(): HasMany
    {
        return $this->hasMany(Proxy::class);
    }
}
