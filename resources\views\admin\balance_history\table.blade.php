<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_balance_history',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatableBalanceHistory;
        $(document).ready(function() {
            DatatableBalanceHistory = window.LaravelDataTables["datatable_balance_history"];
        });
    </script>
    <script>
        $(document).ready(function(){
            $("#model_export_balance_history").click(function(){
                window.location.href = "{{ url('admin/balance-history/export') }}";
            });
        });
    </script>
@endpush
