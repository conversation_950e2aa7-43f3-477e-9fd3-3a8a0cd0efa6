<?php

namespace App\DataTables;

use App\Repositories\TransactionRepository;
use Illuminate\Database\Eloquent\Builder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class TransactionDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->editColumn('amount', function ($query) {
                return number_format($query->amount);
            })
            ->editColumn('type', function ($query) {
                if ($query->type == 'plus') {
                    return '<span class="btn btn-success">'.__('Cộng tiền').'</span>';
                }

                return '<span class="btn btn-danger">'.__('Trừ tiền').'</span>';
            })->rawColumns(['type']);
    }

    public function query(): Builder
    {
        return (new TransactionRepository())->transaction_datatable();
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
//            ->minifiedAjax('/admin/transactions', $this->getScript(), [], ['error' => 'function (err) { defaultOnError(err);}'])
            ->parameters([
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'code' => (new Column([
                'title' => __('Mã GD'),
                'data' => 'code',
                'searchable' => true,
                'orderable' => false,
            ])),
            //            'from_name' => (new Column([
            //                'title' => __('Người chuyển'),
            //                'data' => 'from_name',
            //                'searchable' => true,
            //                'orderable' => false,
            //            ])),
            'amount' => (new Column([
                'title' => __('Số tiền'),
                'data' => 'amount',
                'searchable' => true,
                'orderable' => false,
            ])),
            'type' => (new Column([
                'title' => __('Loại'),
                'data' => 'type',
                'searchable' => true,
                'orderable' => false,
            ])),
            'note' => (new Column([
                'title' => __('Nội dung'),
                'data' => 'note',
                'searchable' => true,
                'orderable' => false,
            ])),
            'created_at' => (new Column([
                'title' => __('Thời gian'),
                'data' => 'created_at',
                'searchable' => true,
                'orderable' => false,
            ])),
        ];
    }

    protected function filename(): string
    {
        return __('Danh sách ngân hàng').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
