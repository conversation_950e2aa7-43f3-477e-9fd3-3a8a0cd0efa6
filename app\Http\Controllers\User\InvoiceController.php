<?php

namespace App\Http\Controllers\User;

use App\DataTables\InvoiceDataTable;
use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Order;
use App\Repositories\InvoiceRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class InvoiceController extends Controller
{
    private InvoiceRepository $invoice;

    public function __construct(
        InvoiceRepository $invoiceRepository,
    ) {
        $this->invoice = $invoiceRepository;
    }

    public function index(InvoiceDataTable $invoiceDataTable)
    {
        $userId = Auth::id();
        if ($userId != 1) {
            $invoiceDataTable->setTemporaryUserId($userId);
        }

        return $invoiceDataTable->render('user.invoice.index');
    }

    public function status(int $id): JsonResponse
    {
        $query_invoice = $this->invoice->find($id);
        if ($query_invoice) {
            $query_invoice->order()->update(['status' => Order::SUCCESS]);

            return $this->success('Xác nhận đơn hàng thanh toán thành công!');
        }

        return $this->error();
    }

    public function block(int $id): JsonResponse
    {
        $query_invoice = $this->invoice->find($id);
        if ($query_invoice) {
            $query_invoice->order()->update(['status' => Order::CANCEL]);

            return $this->success('Từ chối đơn hàng thành công!');
        }

        return $this->error();
    }

    public function destroy(int $id): JsonResponse
    {
        $query = $this->invoice->delete($id);
        if ($query) {

            return $this->success('Xoá hóa đơn thành công');
        }

        return $this->error();
    }

    public function detail(string $id)
    {
        $query_invoice = Invoice::query()->where('id', '==', $id)->get();
        try {
            if ($query_invoice) {
                $data = Invoice::query()
                    ->join('orders', 'invoices.order_id', '=', 'orders.id')
                    ->join('rent_packages', 'orders.rent_package_id', '=', 'rent_packages.id')
                    ->join('users', 'rent_packages.user_id', '=', 'users.id')
                    ->join('packages', 'rent_packages.package_id', '=', 'packages.id')
                    ->where('invoices.id', $id)
                    ->where('users.id', Auth::id())
                    ->select('packages.*', 'invoices.code as invoice_code', 'orders.status as order_status', 'users.name as user_name', 'users.email as user_email', 'users.phone as user_phone', 'users.address as user_address')
                    ->first()
                    ->toArray();

                return view('user.invoice.invoice_detail', ['data' => $data]);
            }
        } catch (\Throwable $e) {
        }

        return response()->json(['error' => 'Hóa đơn không tồn tại'], 404);
    }
}
