@include('admin.user.table')
@include('admin.user.modal_update_commission')
@push('scripts')
    <script type="text/javascript">
        $(document).on('click', '.destroy_user', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: '{{__('Bạn có chắc chắn muốn xoá người dùng này không?')}}',
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.delete('users/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.title, response.data.message)
                                DatatableUser.ajax.reload(null, false);
                                DatatableDeleteUser.ajax.reload(null, false);
                            } else {
                                mess_error(response.data.title, response.data.message)
                            }
                        });
                }
            });
        });
    </script>
    <script type="text/javascript">
        $(document).on('click', '.status_user', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            let status = $(this).data('status');
            let text = '{{__('Bạn có chắc chắn muốn khoá người dùng này không?')}}'
            if (status === 0) {
                text = '{{__('Bạn có chắc chắn muốn mở khoá người dùng này không?')}}'
            }
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: text,
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.put('{{ route('admin.user.index') }}/status/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.message)
                                DatatableUser.ajax.reload(null, false);
                            } else {
                                mess_error(response.data.message)
                            }
                        });
                }
            });
        });
    </script>
    <script type="text/javascript">
        $(document).on('click', '.role_user', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            let type = $(this).data('type');
            let text = '{{__('Bạn có chắc chắn muốn hạ admin này thành người dùng không?')}}'
            if (type === 0) {
                text = '{{__('Bạn có chắc chắn muốn nâng  người dùng này trở thành admin không?')}}'
            }
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: text,
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.put('{{ route('admin.user.index') }}/role/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.message)
                                DatatableUser.ajax.reload(null, false);
                            } else {
                                mess_error(response.data.message)
                            }
                        });
                }
            });
        });
    </script>
@endpush
