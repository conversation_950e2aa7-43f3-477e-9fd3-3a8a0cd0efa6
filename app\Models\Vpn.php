<?php

namespace App\Models;

use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Vpn extends Model
{
    use Cachable, HasFactory;

    //type
    const FREE = 0;

    const PAID = 1;

    protected $table = 'vpn';

    /**
     * @var array
     */
    protected $fillable = [
        'ip_address',
        'name',
        'username',
        'password',
        'flag',
        'country',
        'config_udp',
        'config_tcp',
        'type',
        'city',
        'state',
        'zipcode',
        'isp',
    ];

    protected $casts = [
        'type' => 'integer',
    ];

    public function rent_package(): hasOneThrough
    {
        return $this->hasOneThrough(RentPackage::class, RentPackageVpn::class, 'vpn_id', 'id', 'id', 'rent_package_id');
    }
}
