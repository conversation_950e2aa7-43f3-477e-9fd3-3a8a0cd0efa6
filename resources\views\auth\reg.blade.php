<!DOCTYPE html>
<html lang=en>

<head>
    <meta charset=utf-8>
    <title>{{ config('app.name') }}</title>
    <meta content="width=device-width, initial-scale=1, shrink-to-fit=no" name=viewport>
    <meta content="{{ csrf_token() }}" name=csrf-token>
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" rel=stylesheet>
    <link href="{{ asset('assets/css/pages/login/login-2.css') }}" rel=stylesheet>
    <link href="{{ asset('assets/plugins/global/plugins.bundle.css') }}" rel=stylesheet>
    <link href="{{ asset('assets/css/style.bundle.css') }}" rel=stylesheet>
    <link href="{{ asset('images/favicon.ico') }}" rel="shortcut icon"/>
</head>
<body id="kt_body"
      class="page-loading-enabled page-loading header-fixed header-mobile-fixed subheader-enabled subheader-fixed aside-enabled aside-fixed aside-minimize-hoverable page-loading">
<div class="page-loader page-loader-logo">
    <div class="spinner spinner-primary"></div>
</div>
<div class="d-flex flex-column flex-root">
    <div class="bg-white d-flex flex-column flex-column-fluid flex-lg-row login login-2 login-signin-on" id=kt_login>
        <div class="d-flex flex-row-auto login-aside order-2 order-lg-1 overflow-hidden position-relative">
            <div class="d-flex flex-column flex-column-fluid justify-content-between px-7 px-lg-35 py-9 py-lg-13">
                <span class="pt-2 text-center">
                    <img alt class=max-h-75px id="logo" src="{{ asset('images/site_logo/logo_2.png') }}"/>
                </span>
                <div class="d-flex flex-center flex-column flex-column-fluid">
                    <div class="login-form login-signin py-11">
                        <form class="form" id="kt_login_register_form" novalidate>
                            @csrf
                            <div class="pb-8 text-center">
                                <h2 class="font-size-h1-lg font-size-h2 font-weight-bolder text-dark">{{ __('Đăng ký tài khoản') }}</h2>
                                <p class="font-size-h4 font-weight-bold text-muted">{{ __('Điền đầy đủ các thông tin') }}
                            </div>
                            <div class="form-group">
                                <label for="register_name">{{ __('Họ và tên') }}:</label>
                                <input id="register_name" class="form-control form-control-solid" type="text"
                                       placeholder="{{ __('Họ và tên') }}"
                                       name="name"/>
                            </div>
                            <div class="form-group">
                                <label for="register_email">{{ __('Email') }}:</label>
                                <input id="register_email" class="form-control form-control-solid" type="text"
                                       placeholder="{{ __('Email') }}"
                                       name="email"/>
                            </div>
                            <div class="form-group">
                                <label for="register_phone">{{ __('Số điện thoại') }}:</label>
                                <input id="register_phone" class="form-control form-control-solid" type="text"
                                       placeholder="{{ __('Số điện thoại') }}"
                                       name="phone"/>
                            </div>
                            <div class="form-group">
                                <label for="register_password">{{ __('Mật khẩu') }}:</label>
                                <input id="register_password" class="form-control form-control-solid" type="password"
                                       placeholder="{{ __('Mật khẩu') }}"
                                       name="password"/>
                            </div>
                            <div class="form-group">
                                <label for="register_password_confirm">{{ __('Nhập lại mật khẩu') }}:</label>
                                <input id="register_password_confirm" class="form-control form-control-solid" type="password"
                                       placeholder="{{ __('Nhập lại mật khẩu') }}"
                                       name="password_confirm"/>
                            </div>
                            <a href="#" id="show_invite_code"> <label for="register_invite_code">{{ __('Mã giới thiệu') }}:</label></a>
                            <div class="form-group" id="invite_code_section" style="display: none;">
                                <input id="register_invite_code" class="form-control form-control-solid" type="text"
                                       placeholder="{{ __('Nhập mã giới thiệu') }}" value="{{ session('invite_code') }}"
                                       name="invite_code"/>
                            </div>
                            <div class="d-flex flex-center flex-wrap form-group pb-3 pb-lg-0">
                                <button class="btn btn-primary font-size-h6 font-weight-bolder mx-4 my-3 px-8 py-4" id=kt_login_register_submit type="button">{{ __('Đăng ký') }}</button>
                                <button class="btn btn-light-primary font-size-h6 font-weight-bolder mx-4 my-3 px-8 py-4" id=kt_login_register_cancel type="button">{{ __('Thoát') }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="content d-flex flex-column order-1 order-lg-2 pb-0 w-100" style=background-color:#b1dced>
            <div class="d-flex flex-column justify-content-center pt-5 pt-lg-40 pt-md-5 pt-sm-5 px-7 px-lg-0 text-center">
                <h3 class="display4 font-weight-bolder my-7 text-dark" style=color:#986923>{{ config('app.name') }}</h3>
                <p class="font-size-h2-md font-size-lg font-weight-bolder opacity-70 text-dark">{{__('Hệ thống quản lý Proxy')}}
            </div>
            <div class="bgi-no-repeat bgi-position-x-center bgi-position-y-bottom content-img d-flex flex-row-fluid"
                 style="background-image:url({{asset('assets/media/svg/illustrations/login-visual-2.svg')}})">
            </div>
        </div>
    </div>
</div>
<script>let KTAppSettings = { "breakpoints": { "sm": 576, "md": 768, "lg": 992, "xl": 1200, "xxl": 1400 }, "colors": { "theme": { "base": { "white": "#ffffff", "primary": "#3699FF", "secondary": "#E5EAEE", "success": "#1BC5BD", "info": "#8950FC", "warning": "#FFA800", "danger": "#F64E60", "light": "#E4E6EF", "dark": "#181C32" }, "light": { "white": "#ffffff", "primary": "#E1F0FF", "secondary": "#EBEDF3", "success": "#C9F7F5", "info": "#EEE5FF", "warning": "#FFF4DE", "danger": "#FFE2E5", "light": "#F3F6F9", "dark": "#D6D6E0" }, "inverse": { "white": "#ffffff", "primary": "#ffffff", "secondary": "#3F4254", "success": "#ffffff", "info": "#ffffff", "warning": "#ffffff", "danger": "#ffffff", "light": "#464E5F", "dark": "#ffffff" } }, "gray": { "gray-100": "#F3F6F9", "gray-200": "#EBEDF3", "gray-300": "#E4E6EF", "gray-400": "#D1D3E0", "gray-500": "#B5B5C3", "gray-600": "#7E8299", "gray-700": "#5E6278", "gray-800": "#3F4254", "gray-900": "#181C32" } }, "font-family": "Poppins" };</script>
<script src="{{ url('assets/plugins/global/plugins.bundle.js') }}"></script>
<script src="{{ url('assets/js/scripts.bundle.js') }}"></script>
<script src="{{ url('assets/js/scripts.js') }}"></script>
<script src="{{ url('assets/js/axios.js') }}"></script>
<script type="text/javascript">
    $('#kt_login_register_cancel').on('click', function(e) {
        e.preventDefault();
        window.location.href = '/login';
    });
</script>
<script type="text/javascript">
    var validation_register;
    validation_register = FormValidation.formValidation(
        KTUtil.getById('kt_login_register_form'), {
            fields: {
                token_reset: {
                    validators: {
                        notEmpty: {
                            message: '{{ __('Vui lòng điền mã xác nhận') }}'
                        }
                    }
                },
                phone: {
                    validators: {
                        notEmpty: {
                            message: '{{ __('Vui lòng điền mật khẩu') }}'
                        },
                    }
                },
                password: {
                    validators: {
                        notEmpty: {
                            message: '{{ __('Vui lòng điền mật khẩu') }}'
                        },
                        checkPassword: {
                            message: '{{ __('Vui lòng nhập mật khẩu mạnh hơn (bao gồm cả chữ thường và chữ hoa và số)') }}'
                        },
                    }
                },
                password_confirm: {
                    validators: {
                        notEmpty: {
                            message: '{{ __('Vui lòng nhập lại mật khẩu') }}'
                        },
                        identical: {
                            compare: function() {
                                return form.querySelector('[name="register_password"]').value;
                            },
                            message: '{{ __('Nhập lại mật khẩu không trùng khớp') }}'
                        }
                    }
                },
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap()
            }
        }
    );
    $('#kt_login_register_submit').on('click', function(e) {
        e.preventDefault();
        var csrfToken = $('#kt_login_register_form input[name="_token"]').val();

        validation_register.validate().then(function(status) {
            if (status !== 'Valid') {
                mess_trial()
            } else {
                var urlParams = new URLSearchParams(window.location.search);
                var ref = urlParams.get('invite_code');
                axios.post('register?invite_code', {
                    _token: csrfToken,
                    name: $("#register_name").val(),
                    email: $("#register_email").val(),
                    phone: $("#register_phone").val(),
                    password: $("#register_password").val(),
                    invite_code: ref,
                }).then(function(response) {
                    if (response.data.status) {
                        mess_success(response.data.title,response.data.message)
                        setTimeout(function() {
                            window.location.href = '/login';
                        }, 2000);
                        // _showForm('login');
                    } else {
                        mess_error(response.data.title,response.data.message)
                    }
                });
            }
        });
    });
</script>
<script>
    document.getElementById('show_invite_code').addEventListener('click', function(event) {
        event.preventDefault();

        var inviteCodeSection = document.getElementById('invite_code_section');
        if (inviteCodeSection.style.display === 'none') {
            inviteCodeSection.style.display = 'block';
        } else {
            inviteCodeSection.style.display = 'none';
        }
    });
</script>
@include('component.admin.script')
</body>
