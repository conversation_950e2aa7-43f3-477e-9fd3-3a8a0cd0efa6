<?php

namespace App\Casts;

use Carbon\Carbon;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class DateTimeCast implements CastsAttributes
{
    public function get($model, $key, $value, $attributes)
    {
        return Carbon::parse($value)->setTimezone(config('timezone'))->toDateTimeString();
    }

    public function set($model, $key, $value, $attributes)
    {
        return $value;
    }
}
