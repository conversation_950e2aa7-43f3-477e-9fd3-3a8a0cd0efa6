<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vpn', function (Blueprint $table) {
            $table->id();
            $table->string('ip_address')->unique();
            $table->string('name');
            $table->integer('type')->default(0); //free or purchase
            $table->string('country');
            $table->string('flag');
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('zipcode')->nullable();
            $table->string('isp')->nullable();
            $table->string('username')->nullable();
            $table->string('password')->nullable();
            $table->longText('config_tcp')->nullable();
            $table->longText('config_udp')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vpn');
    }
};
