<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegisterRequest;
use App\Models\Affiliate;
use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

class RegisterController extends Controller
{
    private UserRepository $user;

    public function __construct(UserRepository $userRepository)
    {
        $this->user = $userRepository;
    }

    public function index(): View
    {
        return view('auth.reg');
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        $input = array_map('trim', $request->all());
        if (! $this->valid($input['email']) || ! $this->valid($input['phone']) || ! $this->valid($input['password']) || ! $this->valid($input['name'])) {
            return $this->error('Vui lòng điền đầy đủ thông tin');
        }
        if (! filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
            return $this->error('Vui lòng điền đúng định dạng email');
        }
        if (! preg_match('/^[0-9]{10}+$/', $input['phone'])) {
            return $this->error('Vui lòng điền đúng định dạng số điện thoại');
        }
        $query = $this->user->exitsColumn('email', $input['email']);
        if ($this->valid($query)) {
            return $this->error('Đã tồn tại email này rồi');
        }
        $query = $this->user->exitsColumn('phone', $input['phone']);
        if ($this->valid($query)) {
            return $this->error('Đã tồn tại số điện thoại này rồi');
        }
        $q = User::query()->create([
            'name' => $input['name'],
            'email' => $input['email'],
            'phone' => $input['phone'],
            'password' => bcrypt($input['password']),
            'role' => '-1',
        ]);
        $inviteCode = $request->input('invite_code');
        $referrer = User::query()->where('referrer_code', '=', $inviteCode)->first();
        if ($referrer) {
            Affiliate::query()->create(
                [
                    'referrer_id' => $referrer->id,
                    'user_id' => $q->id,
                ]
            );
        }
        if ($q) {
            return $this->success('Đăng ký thành công!');
        }

        return $this->error('Sai!');
    }
}
