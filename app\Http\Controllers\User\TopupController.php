<?php

namespace App\Http\Controllers\User;

use App\DataTables\TopupDataTable;
use App\Http\Controllers\Controller;
use App\Models\Topup;
use App\Services\BalanceService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TopupController extends Controller
{
    private BalanceService $balance;

    public function __construct(
        BalanceService $balanceService,
    ) {
        $this->balance = $balanceService;
    }

    public function index(TopupDataTable $topupDataTable): mixed
    {
        $topup = Topup::where('topup.user_id', Auth::id())
            ->latest('topup.created_at')
            ->first();
        $is_topup = 1;
        if ($topup) {
            $createdAt = $topup->created_at;
            $twoHour = $createdAt->addHour(2);
            $is_topup = ($topup->status == Topup::CANCEL || $topup->status == Topup::SUCCESS) ? 0 : $is_topup;
            if ((Carbon::now()->greaterThanOrEqualTo($twoHour) && $topup->status == Topup::PENDING)) {
                $is_topup = 0;
                $topup->status = Topup::SUCCESS;
                $topup->save();
            }
        }

        return $topupDataTable->render('user.topup.index', ['data' => $topup, 'is_topup' => $is_topup]);
    }

    public function create(Request $request)
    {
        $request->validate([
            'amount' => 'required|integer|min:0',
            'code' => 'required|string|max:255',
        ]);
        $topup = new Topup();
        $topup->amount = $request->input('amount');
        $topup->code = $request->input('code');
        $topup->user_id = Auth::id();
        $topup->account_id = '1';
        $topup->save();

        return redirect()->route('user.topup.create');
    }

    public function cancel(int $id)
    {
        $topup = Topup::where('user_id', Auth::id())->latest('created_at')->first();
        $topup->status = 2;
        $topup->save();
        if ($topup) {
            return $this->success(data: $topup);
        }

        return $this->error();
    }
}
