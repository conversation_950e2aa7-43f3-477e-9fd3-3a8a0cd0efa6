@if($is_topup == 0 || !isset($data))
    <div class="card-body">
        <div class="row">
            <div class="col-sm-12">
                <p><b class="text-danger">M<PERSON> chuyển tiền tự động chỉ có tác dụng cho 1 lần chuyển khoản trong vòng 1 giờ kể từ khi tạo, nếu quá thời hạn vui lòng tạo mã mới.</b></p>
                <a class="mb-3 btn-transition btn-block btn btn-outline-info" data-toggle="modal" data-target="#kt_modal_new2">
                    <span class="svg-icon svg-icon-primary svg-icon-2x" >
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <rect fill="#000000" x="4" y="11" width="16" height="2" rx="1"/>
                                <rect fill="#000000" opacity="0.3" transform="translate(12.000000, 12.000000) rotate(-270.000000) translate(-12.000000, -12.000000) " x="4" y="11" width="16" height="2" rx="1"/>
                            </g>
                        </svg>
                    </span>
                    <span class="badge badge-success badge-dot badge-dot-sm badge-dot-inside"></span>TẠO MÃ CHUYỂN KHOẢN
                </a>
            </div>
        </div>
    </div>
@endif

@if($is_topup == 1 && isset($data))
    <div class="card-body border-top p-9 d-flex flex-wrap">
        <div class="col-lg-6 col-12">
            <div class="row mb-1">
                <label class="col-sm-5 col-4 col-form-label fw-semibold fs-5">Ngân hàng:</label>
                <div class="d-flex col-sm-7 col-auto col-form-label fw-semibold fs-5">
                    <label class="col-form-label fw-semibold p-0" id="copy_bank">Ngân hàng Thương mại Cổ phần Á Châu (ACB)</label>
                </div>
            </div>
            <div class="row mb-1">
                <label class="col-sm-5 col-form-label col-4 fw-semibold fs-5">Chủ tài khoản:</label>
                <div class="d-flex col-sm-7 col-auto col-form-label fw-semibold fs-5">
                    <label class="col-form-label fw-semibold p-0">TRAN TAN KHOA</label>
                </div>
            </div>
            <div class="row mb-1">
                <label class="col-sm-5 col-form-label col-4 fw-semibold fs-5">Số tài khoản:</label>
                <div class="d-flex col-sm-7 col-auto col-form-label fw-semibold fs-5">
                    <label class="col-form-label fw-semibold p-0" id="copy_stk">********</label>
                    <button data-action="copy" data-id="#copy_stk" class="w-100px btn btn-active-color-primary btn-color-gray-500 btn-icon btn-sm btn-outline-light" style="margin-top: -5px">
                    <span class="svg-icon svg-icon-primary svg-icon-2x">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <rect x="0" y="0" width="24" height="24"/>
                                <path d="M6,9 L6,15 C6,16.6568542 7.********,18 9,18 L15,18 L15,18.8181818 C15,20.2324881 14.2324881,21 12.8181818,21 L5.********,21 C3.********,21 3,20.2324881 3,18.8181818 L3,11.1818182 C3,9.******** 3.********,9 5.********,9 L6,9 Z" fill="#000000" fill-rule="nonzero"/>
                                <path d="M10.1818182,4 L17.8181818,4 C19.2324881,4 20,4.******** 20,6.******** L20,13.8181818 C20,15.2324881 19.2324881,16 17.8181818,16 L10.1818182,16 C8.********,16 8,15.2324881 8,13.8181818 L8,6.******** C8,4.******** 8.********,4 10.1818182,4 Z" fill="#000000" opacity="0.3"/>
                            </g>
                        </svg>
                    </span>
                        Sao chép
                    </button>
                </div>
            </div>
            <div class="row mb-1">
                <label class="col-sm-5 col-4 col-form-label fw-semibold fs-5">Số tiền:</label>
                <div class="d-flex col-sm-7 col-auto col-form-label fw-semibold fs-5">
                    <label class="col-form-label fw-semibold p-0">{{ number_format($data['amount']) }} đồng</label>
                </div>
            </div>
            <div class="row">
                <label class="col-sm-5 col-4 col-form-label fw-semibold fs-5">Nội dung chuyển tiền:</label>
                <div class="d-flex col-sm-7 col-8 col-form-label fw-semibold fs-5">
                    <label class="col-form-label fw-semibold p-0" id="copy_content_bank" >{{ $data['code'] }}</label>
                    <button data-action="copy" data-id="#copy_content_bank" class="w-100px btn btn-active-color-primary btn-color-gray-500 btn-icon btn-sm btn-outline-light" style="margin-top: -5px">
                    <span class="svg-icon svg-icon-primary svg-icon-2x">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <rect x="0" y="0" width="24" height="24"/>
                                <path d="M6,9 L6,15 C6,16.6568542 7.********,18 9,18 L15,18 L15,18.8181818 C15,20.2324881 14.2324881,21 12.8181818,21 L5.********,21 C3.********,21 3,20.2324881 3,18.8181818 L3,11.1818182 C3,9.******** 3.********,9 5.********,9 L6,9 Z" fill="#000000" fill-rule="nonzero"/>
                                <path d="M10.1818182,4 L17.8181818,4 C19.2324881,4 20,4.******** 20,6.******** L20,13.8181818 C20,15.2324881 19.2324881,16 17.8181818,16 L10.1818182,16 C8.********,16 8,15.2324881 8,13.8181818 L8,6.******** C8,4.******** 8.********,4 10.1818182,4 Z" fill="#000000" opacity="0.3"/>
                            </g>
                        </svg>
                    </span>
                        Sao chép
                    </button>
                </div>
            </div>
            <div class="row">
                <div class="d-flex col-sm-5 col-5 col-form-label fw-semibold ">
                    <button id="checkButton" class="btn btn-light-primary w-100">Kiểm tra</button>
                </div>
                <div class="d-flex col-sm-5 col-5 col-form-label fw-semibold ">
                    <button id="cancelButton" class="btn btn-light-danger w-100" data-id="{{ $data['id'] }}">Hủy bỏ</button>
                </div>
            </div>
        </div>
        <div class="col-lg-6 d-flex justify-content-center">
            <img src="https://img.vietqr.io/image/ACB-********-qr_only.png?amount={{ ($data['amount']) }}&addInfo={{ $data['code'] }}&accountName=VU DO VY BINH" class="max-w-300px object-fit-contain" alt="">
        </div>
        <div class="w-100">
            <p class="text-danger">*Lưu ý:
                <br>
                - Thời gian sử lý giao dịch có thể từ 10p đến 1 giờ <br>
                - Nạp tối thiểu: 10,000 đ. Cố tình nạp dưới mức tối thiểu sai cú pháp không hỗ trợ dưới mọi hình thức.
            </p>
        </div>
    </div>
@endif
<hr>
<div class="card-header flex-wrap py-5">
    <div class="card-title">
        <h3 class="card-label">
            Thông tin chi tiết
        </h3>
    </div>
</div>
<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_topup',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatableTopup;
        $(document).ready(function() {
            DatatableTopup = window.LaravelDataTables["datatable_topup"];
        });
        $(document).ready(function(){
            $('[data-action="copy"]').click(function(){
                var dataId = $(this).data('id');
                var contentToCopy = $(dataId).html();
                var tempTextarea = $('<textarea>');
                tempTextarea.val(contentToCopy);
                $('body').append(tempTextarea);
                tempTextarea.select();
                document.execCommand('copy');
                tempTextarea.remove();

                $(this).addClass('bg-success text-inverse-success');

                setTimeout(function(){
                    $(this).removeClass('bg-success text-inverse-success');
                }.bind(this), 2000);

                var originalText = $(this).text();
                $(this).text('Đã sao chép');

                setTimeout(function(){
                    $(this).text(originalText);
                }, 2000);
            });
        });
        var coinInput = document.getElementById('amount');
        var codeInput = document.getElementById('code');

        coinInput.addEventListener('input', function () {
            var coinValue = coinInput.value.replace(/,/g, ''); // Xóa dấu phẩy cũ

            coinInput.value = coinValue;
            var currentTime = new Date();
            var second = currentTime.getSeconds();
            var hours = currentTime.getHours();
            var minutes = currentTime.getMinutes();
            var day = currentTime.getDate();
            var month = currentTime.getMonth() + 1; // Tháng bắt đầu từ 0
            var transferContent = 'NT' + hours + minutes + second + day + month + ' ' + coinValue;

            codeInput.value = transferContent;
        });

        function formatNumberWithCommas(number) {
            return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        let validation_create_topup = FormValidation.formValidation(
            KTUtil.getById('kt_modal_new_form'), {
                fields: {
                    amount: {
                        validators: {
                            notEmpty: {
                                message: 'Số tiền không được để trống'
                            },
                            greaterThan: {
                                value: 10000,
                                message: 'Số tiền phải lớn hơn 10,000'
                            }
                        }
                    },
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap()
                }
            }
        );
        $('#kt_modal_new_form').submit(function (e) {
            e.preventDefault();
            let form = $(this);
            validation_create_topup.validate().then(function () {
                axios({
                    method: 'POST',
                    url: '/topup',
                    data: form.serialize(),
                }).then((response) => {
                    if (response.data.status) {
                        mess_success(response.data.title, response.data.message)
                        DatatableTopup.ajax.reload(null, false);
                    } else {
                        mess_error(response.data.title, response.data.message)
                    }
                });

            });
        });

    </script>
@endpush


