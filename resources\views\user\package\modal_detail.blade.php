<div class="modal fade" id="model_proxy_detail" tabindex="-1" role="dialog" aria-labelledby="labelModelEdit"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Thông tin proxy') }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="form">
                    <div class="card-body">
                        <div class="form-group row">
                            <div class="col-6">
                                <label for="ip_address">{{ __('Server IP') }}:</label>
                                <input readonly id="ip_address" name="ip_address" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Server IP') }}" />
                            </div>
                            <div class="col-3">
                                <label for="http_port">{{ __('Cổng http') }}:</label>
                                <input readonly id="http_port" name="http_port" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Cổng http') }}" />
                            </div>
                            <div class="col-3">
                                <label for="socks5_port">{{ __('Cổng socks5') }}:</label>
                                <input readonly id="socks5_port" name="socks5_port" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Cổng socks5') }}" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-6">
                                <label for="username">{{ __('Tài khoản') }}:</label>
                                <input readonly id="username" name="username" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Tài khoản') }}" />
                            </div>
                            <div class="col-6">
                                <label for="password">{{ __('Mật khẩu') }}:</label>
                                <input readonly id="password" name="password" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Mật khẩu') }}" />
                            </div>

                        </div>
                        <div class="form-group row">
                            <div class="col-6">
                                <label for="country">{{ __('Quốc gia') }}:</label>
                                <input readonly id="country" name="country" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Quốc gia') }}" />
                            </div>
                            <div class="col-3">
                                <label for="state">{{ __('Tỉnh/Thành') }}:</label>
                                <input readonly id="state" name="state" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Tỉnh/Thành') }}" />
                            </div>
                            <div class="col-3">
                                <label for="city">{{ __('Quận/Huyện') }}:</label>
                                <input readonly id="city" name="city" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Quận/Huyện') }}" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-6">
                                <label for="zipcode">{{ __('Zipcode') }}:</label>
                                <input readonly id="zipcode" name="zipcode" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Zipcode') }}" />
                            </div>
                            <div class="col-6">
                                <label for="isp">{{ __('Isp') }}:</label>
                                <input readonly id="isp" name="isp" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Isp') }}" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-6">
                                <label for="reset_time">{{ __('Thời gian xoay') }}:</label>
                                <input readonly id="reset_time" name="reset_time" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Thời gian xoay') }}" />
                            </div>
                            <div class="col-6">
                                <label for="expired_at">{{ __('Thời gian sử dụng') }}:</label>
                                <input readonly id="expired_at" name="expired_at" type="text" class="form-control form-control-solid"
                                       placeholder="{{ __('Thời gian sử dụng') }}" />
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
