<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\hasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    //type
    const ADMIN = 0;

    const USER = 1;

    //status
    const ACTIVE = 0;

    const DISABLE = 1;

    protected $table = 'users';

    /**
     * @var array
     */
    protected $fillable = [
        'avatar',
        'name',
        'email',
        'password',
        'type',
        'created_by',
        'last_login_at',
        'deleted_at',
        'token_reset',
        'remember_token',
        'gender',
        'phone',
        'brith_day',
        'address',
        'status',
        'commission',
        'referrer_code',
        'balance',
    ];

    /**
     * @var array
     */
    protected $hidden = [
        'password',
        'token_reset',
        'remember_token',
    ];

    /**
     * @var array
     */
    protected $casts = [
        'type' => 'integer',
        'created_by' => 'integer',
        'gender' => 'integer',
    ];

    public function vpns(): hasManyThrough
    {
        return $this->hasManyThrough(Vpn::class, UserVpn::class, 'user_id', 'id', 'id', 'vpn_id');
    }
}
