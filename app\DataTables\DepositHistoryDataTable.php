<?php

namespace App\DataTables;

use App\Repositories\DepositHistoryRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as YajraBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class DepositHistoryDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        $dataTable = new EloquentDataTable($query);

        return $dataTable->addIndexColumn()
            ->editColumn('created_at', function ($query) {
                return Carbon::parse($query->created_at)->toDateTimeString();
            })
            ->editColumn('amount', function ($query) {
                return number_format($query->amount);
            });
    }

    private $temporaryUserId;

    public function setTemporaryUserId($userId)
    {
        $this->temporaryUserId = $userId;
    }

    public function query(): Builder
    {
        $query = (new DepositHistoryRepository())->datatable();

        if ($this->temporaryUserId !== null) {
            $query->where('user_id', $this->temporaryUserId);
        }

        return $query;
    }

    public function html(): YajraBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax('', $this->getScript(), [], ['error' => 'function (err) { defaultOnError(err);}'])
            ->parameters([
                'order' => [[0, 'desc']],
                'responsive' => true,
                'language' => __('datatable'),
                'lengthMenu' => [[10, 50, 100, 200, -1], [10,  50, 100, 200, 'All']],
                'fnDrawCallback' => "function(oSettings) {
                    if (oSettings._iDisplayLength > oSettings.fnRecordsDisplay()) {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').hide();
                    } else {
                        $(oSettings.nTableWrapper).find('.dataTables_paginate').show();
                    }
                }",
            ]);
    }

    protected function getColumns(): array
    {
        return [
            'dt_rowindex' => (new Column([
                'title' => __('STT'),
                'data' => 'DT_RowIndex',
                'className' => 'text-center',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false,
            ])),
            'user.email' => (new Column([
                'title' => __('Email'),
                'data' => 'user.email',
                'searchable' => true,
                'orderable' => false,
            ])),
            'user.name' => (new Column([
                'title' => __('Tên'),
                'data' => 'user.name',
                'searchable' => true,
                'orderable' => false,
            ])),
            'amount' => (new Column([
                'title' => __('Số tiền'),
                'data' => 'amount',
                'searchable' => true,
                'orderable' => false,
            ])),
            'type' => (new Column([
                'title' => __('Loại'),
                'data' => 'type',
                'searchable' => true,
                'orderable' => false,
            ])),
            'description' => (new Column([
                'title' => __('Mô tả'),
                'data' => 'description',
                'searchable' => true,
                'orderable' => false,
            ])),
            'created_at' => (new Column([
                'title' => __('Thời gian'),
                'data' => 'created_at',
                'searchable' => true,
                'orderable' => false,
            ])),

        ];
    }

    protected function filename(): string
    {
        return __('deposit_history').'_'.time();
    }

    private function getScript(): string
    {
        return '';
    }
}
