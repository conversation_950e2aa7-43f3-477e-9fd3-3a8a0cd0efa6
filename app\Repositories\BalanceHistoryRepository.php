<?php

namespace App\Repositories;

use App\Models\BalanceHistory;
use Illuminate\Database\Eloquent\Builder;

class BalanceHistoryRepository extends BaseRepository
{
    public function model(): string
    {
        return BalanceHistory::class;
    }

    public function datatable(): Builder
    {
        return $this->query()->with(['user' => function ($query) {
            $query->withTrashed();
        }]);
    }
}
