<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\RentPackage;
use App\Models\Vpn;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\DataTables;

class VpnController extends Controller
{
    public function index(): JsonResponse
    {
        $query = Vpn::query()->select('id', 'ip_address', 'name', 'type', 'flag', 'country', 'state', 'city');

        return DataTables::of($query)
            ->addIndexColumn()
            ->make();
    }

    public function package(): JsonResponse
    {
        $query_find = RentPackage::query()
            ->select('id', 'package_id', 'expired_at')
            ->with('package:id,name')
            ->where('status', '=', RentPackage::ACTIVE)
            ->where('user_id', '=', Auth::id())
            ->where('expired_at', '>', Carbon::now())
            ->whereHas('package', function ($query) {
                $query->where('type', '=', 'vpn');
            })
            ->first();
        if ($query_find) {
            return $this->success(data: $query_find);
        }

        return $this->error();
    }

    public function show(int $id): JsonResponse
    {
        $query_find = Vpn::query()->whereKey($id)->first(['id', 'name', 'type', 'ip_address', 'country', 'flag', 'username', 'password', 'config_udp', 'config_tcp']);
        if ($query_find) {
            if ($query_find['type'] === Vpn::PAID) {
                $query_exists = RentPackage::query()
                    ->where('status', '=', RentPackage::ACTIVE)
                    ->where('user_id', '=', Auth::id())
                    ->where('expired_at', '>', Carbon::now())
                    ->whereHas('package', function ($query) {
                        $query->where('type', '=', 'vpn');
                    })
                    ->exists();
                if (! $query_exists) {
                    return $this->error();
                }
            }
            $result = $query_find->toArray();
            $result['protocol'] = $query_find->config_udp ? 'udp' : 'tcp';
            $result['config'] = $query_find->config_udp ?: $query_find->config_tcp;
            unset($result['config_udp']);
            unset($result['config_tcp']);

            return $this->success(data: $result);
        }

        return $this->error();
    }

    public function random(Request $request): JsonResponse
    {
        try {
            $query_exists = RentPackage::query()
                ->where('status', '=', RentPackage::ACTIVE)
                ->where('user_id', '=', Auth::id())
                ->where('expired_at', '>', Carbon::now())
                ->whereHas('package', function ($query) {
                    $query->where('type', '=', 'vpn');
                })
                ->exists();
            $type = $query_exists ? Vpn::PAID : Vpn::FREE;
            $query_find = Vpn::query()
                ->select('id', 'name', 'type', 'ip_address', 'country', 'flag', 'username', 'password', 'config_udp', 'config_tcp')
                ->where('type', '=', $type)
                ->inRandomOrder()
                ->first();
            if ($query_find) {
                $result = $query_find->toArray();
                if ($request->input('protocol') == 'tcp') {
                    $result['protocol'] = 'tcp';
                    $result['config'] = $query_find->config_tcp;
                } elseif (($request->input('protocol') == 'udp')) {
                    $result['protocol'] = 'udp';
                    $result['config'] = $query_find->config_udp;
                } else {
                    $result['protocol'] = $query_find->config_udp ? 'udp' : 'tcp';
                    $result['config'] = $query_find->config_udp ?: $query_find->config_tcp;
                }
                unset($result['config_udp']);
                unset($result['config_tcp']);

                return $this->success(data: $result);
            }
        } catch (\Exception) {
            return $this->error();
        }
    }

    public function allservers(Request $request)
    {
        $per_page = 15;
        try {
            $query = Vpn::query()->select('id', 'name', 'type', 'ip_address', 'country', 'flag');

            if (isset($request->country)) {
                $query = $query->where('country', $request->country);
            }
            if (isset($request->state)) {
                $query = $query->where('state', $request->state);
            }
            if (isset($request->city)) {
                $query = $query->where('city', $request->city);
            }
            if (isset($request->name)) {
                $query = $query->where('name', 'like', '%'.$request->name.'%');
            }

            $countQuery = clone $query;

            $totalItemCount = $countQuery->count();
            $numberOfPages = ceil($totalItemCount / $per_page);

            if ($request->page != null) {
                $page = $request->page;
                $page--;
                if ($page < 0) {
                    $page = 0;
                }
                $query = $query->limit($per_page)->offset($per_page * $page);
            }

            $output = $query->get()->toArray();

            return $this->success($numberOfPages, $output);
        } catch (\Exception $e) {
            return $this->error($e);
        }
    }
}
