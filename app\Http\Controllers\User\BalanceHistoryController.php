<?php

namespace App\Http\Controllers\User;

use App\DataTables\BalanceHistoryDataTable;
use App\Http\Controllers\Controller;
use App\Models\BalanceHistory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;

class BalanceHistoryController extends Controller
{
    public function index(BalanceHistoryDataTable $balanceHistoryDataTable): mixed
    {
        $userId = Auth::id();
        if ($userId != 1) {
            $balanceHistoryDataTable->setTemporaryUserId($userId);
        }

        return $balanceHistoryDataTable->render('user.balance_history.index');
    }

    public function export()
    {
        if (Auth::check()) {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setCellValue('A1', __('STT'));
            $sheet->setCellValue('B1', __('Email'));
            $sheet->setCellValue('C1', __('Tên'));
            $sheet->setCellValue('D1', __('Số tiền'));
            $sheet->setCellValue('E1', __('Loại'));
            $sheet->setCellValue('F1', __('Số dư'));
            $sheet->setCellValue('G1', __('Thời gian'));
            $rowCount = 2;
            $balanceHistories = BalanceHistory::join('users', 'balance_history.user_id', '=', 'users.id')
                ->where('balance_history.user_id', Auth::id())
                ->select('balance_history.*', 'users.name as user_name', 'users.email as user_email')
                ->get();
            foreach ($balanceHistories as $key) {
                $sheet->setCellValue('A'.$rowCount, $key['id']);
                $sheet->setCellValue('B'.$rowCount, $key['user_email']);
                $sheet->setCellValue('C'.$rowCount, $key['user_name']);
                $sheet->setCellValue('D'.$rowCount, $key['amount']);
                $sheet->setCellValue('E'.$rowCount, $key['transaction_type']);
                $sheet->setCellValue('F'.$rowCount, $key['balance_after_transaction']);
                $sheet->setCellValue('G'.$rowCount, $key['created_at']);
                $rowCount++;
            }
            $writer = new Csv($spreadsheet);
            $writer->setUseBOM(true);
            $writer->setOutputEncoding('UTF-8');
            $dt = Carbon::now()->format('d-m-Y');
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="'.__('Biến động số dư').' - '.$dt.'.csv"');
            $writer->save('php://output');

        }
    }
}
