<div class="card-body">
    <div class="mb-7">
        <div class="align-items-center">
            <div class="row align-items-center">
                <div class="col-md-3 my-2 my-md-0">
                    <div class="input-icon">
                        <input type="text" class="form-control" autocomplete="off" placeholder="{{ __('Tìm kiếm') }}" id="search_query" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-md-3 my-2 my-md-0">
                    <div class="d-flex align-items-center">
                        <label for="package_search" class="mr-3 mb-0 d-none d-md-block">{{ __('Gói') }}:</label>
                        <select class="form-control" id="package_search">
                            <option value="">{{ __('Tất cả') }}</option>
                            @isset($package)
                                @if(count($package) > 0)
                                    @foreach ($package as $key => $item)
                                        <option value="{{ $item['name'] }}">{{ $item['name'] }}</option>
                                    @endforeach
                                @endif
                            @endisset
                        </select>
                    </div>
                </div>
                <div class="col-md-3 my-2 my-md-0">
                    <div class="d-flex align-items-center">
                        <label for="status_search" class="mr-3 mb-0 d-none d-md-block">{{ __('Trạng thái') }}:</label>
                        <select class="form-control" id="status_search">
                            <option value="">{{ __('Tất cả') }}</option>
                            <option value="{{ \App\Models\RentPackage::ACTIVE }}">Còn hạn</option>
                            <option value="{{ \App\Models\RentPackage::DISABLE }}">Hết hạn</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_package',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatablePackage;
        $(document).ready(function() {
            DatatablePackage = window.LaravelDataTables["datatable_package"];
        });
        $(document).on('keyup', '#search_query', function () {
            DatatablePackage.search($(this).val(), false, false).draw() ;
        });
        $(document).on('change', '#package_search', function () {
            DatatablePackage.column(2).search($(this).val(), false, false).draw();
        });
        $(document).on('change', '#status_search', function () {
            DatatablePackage.column(6).search($(this).val(), false, false).draw() ;
        });
        $(document).on('click', '.view_proxy', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            axios.get('proxies/' + id)
                .then((response) => {
                    if (response.data.status) {
                        let proxy = response.data.data;
                        $('#id').val(proxy.id);
                        $('#ip_address').val(proxy.server_ip);
                        $('#http_port').val(proxy.http_port);
                        $('#socks5_port').val(proxy.socks5_port);
                        $('#username').val(proxy.username);
                        $('#password').val(proxy.password);
                        $('#server_id').val(proxy.server_id);
                        $('#subdomain_id').val(proxy.subdomain_id);
                        $('#city').val(proxy.city);
                        $('#state').val(proxy.state);
                        $('#data').val(proxy.data);
                        $('#country').val(proxy.country);
                        $('#zipcode').val(proxy.zipcode);
                        $('#isp').val(proxy.isp);
                        $('#org').val(proxy.org);
                        $('#expired_at').val(proxy.expired_at);
                        $('#reset_time').val(proxy.reset_at);
                    } else {
                        mess_error(response.data.title, response.data.message)
                    }
                });
        });
        $("#model_proxy_user").click(function(){
            window.location.href = "{{ url('/proxies/export') }}";
        });
    </script>
@endpush
