<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class ForgotPassMail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private array $data;

    public function __construct(array $data)
    {
        $this->onQueue('ForgotPassMail');
        $this->data = $data;
    }

    public function handle()
    {
        $token = $this->data['token_reset'];
        $email = $this->data['email'];
        $title = __('<PERSON><PERSON>y lại mật khẩu');
        Mail::send('mail.forgot_pass', [
            'token' => $token,
        ], function ($message) use ($title, $email) {
            $message->to($email)->subject($title);
            $message->from(config('mail.from.address'), config('mail.from.name'));
        });
    }
}
