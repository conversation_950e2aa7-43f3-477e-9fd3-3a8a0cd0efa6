<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\DepositHistoryDataTable;
use App\Http\Controllers\Controller;
use App\Models\DepositHistory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;

class DepositHistoryController extends Controller
{
    public function index(DepositHistoryDataTable $depositHistoryDataTable): mixed
    {
        return $depositHistoryDataTable->render('admin.deposit_history.index');
    }

    public function export()
    {
        if (Auth::check()) {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setCellValue('A1', __('STT'));
            $sheet->setCellValue('B1', __('Email'));
            $sheet->setCellValue('C1', __('Tên'));
            $sheet->setCellValue('D1', __('Số tiền'));
            $sheet->setCellValue('E1', __('Loại'));
            $sheet->setCellValue('F1', __('Mô tả'));
            $sheet->setCellValue('G1', __('Thời gian'));
            $rowCount = 2;
            foreach (DepositHistory::all() as $key) {
                $sheet->setCellValue('A'.$rowCount, $key['id']);
                $sheet->setCellValue('B'.$rowCount, $key['user.email']);
                $sheet->setCellValue('C'.$rowCount, $key['user.name']);
                $sheet->setCellValue('D'.$rowCount, $key['amount']);
                $sheet->setCellValue('E'.$rowCount, $key['type']);
                $sheet->setCellValue('F'.$rowCount, $key['description']);
                $sheet->setCellValue('G'.$rowCount, $key['created_at']);
                $rowCount++;
            }
            $writer = new Csv($spreadsheet);
            $writer->setUseBOM(true);
            $writer->setOutputEncoding('UTF-8');
            $dt = Carbon::now()->format('d-m-Y');
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="'.__('Lịch sử lạp tiền').' - '.$dt.'.csv"');
            $writer->save('php://output');

        }
    }
}
