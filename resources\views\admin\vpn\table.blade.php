<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_vpn',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatableVpn;
        $(document).ready(function() {
            DatatableVpn = window.LaravelDataTables["datatable_vpn"];
        });
        $(document).on('click', '.view_vpn', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            axios.get('vpn/' + id)
                .then((response) => {
                    if (response.data.status) {
                        let vpn = response.data.data;
                        $('#update_id').val(vpn.id);
                        $('#update_ip_address').val(vpn.ip_address);
                        $('#update_name').val(vpn.name);
                        $('#update_username').val(vpn.username);
                        $('#update_password').val(vpn.password);
                        $('#update_config_tcp').val(vpn.config_tcp);
                        $('#update_config_udp').val(vpn.config_udp);
                        $('#update_type').val(vpn.type);
                    } else {
                        mess_error(response.data.title, response.data.message)
                    }
                });
        });
    </script>
@endpush
