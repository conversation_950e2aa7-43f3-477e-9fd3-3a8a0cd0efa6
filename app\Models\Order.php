<?php

namespace App\Models;

use App\Casts\DateTimeCast;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Order extends Model
{
    use HasFactory;

    //status
    const PENDING = 0;

    const SUCCESS = 1;

    const CANCEL = 2;

    //type
    const TRIAL = 0;

    const RENT = 1;

    const RENEW = 2;

    /**
     * @var array
     */
    protected $fillable = [
        'rent_package_id',
        'amount',
        'day',
        'status',
        'type',
    ];

    /**
     * @var array
     */
    protected $casts = [
        'rent_package_id' => 'integer',
        'amount' => 'float',
        'day' => 'integer',
        'status' => 'integer',
        'type' => 'integer',
        'created_at' => DateTimeCast::class,
        'updated_at' => DateTimeCast::class,
    ];

    public function rent_package(): HasOne
    {
        return $this->hasOne(RentPackage::class, 'id', 'rent_package_id');
    }

    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class, 'order_id', 'id');
    }
}
