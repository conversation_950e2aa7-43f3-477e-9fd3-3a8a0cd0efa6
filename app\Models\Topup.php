<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Topup extends Model
{
    use HasFactory;

    protected $table = 'topup';

    //status
    const PENDING = 0;

    const SUCCESS = 1;

    const CANCEL = 2;

    /**
     * @var array
     */
    protected $fillable = [
        'user_id',
        'account_id',
        'amount',
        'code',
        'status',
    ];

    /**
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
    ];

    public function user(): belongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'description', 'code');
    }
}
