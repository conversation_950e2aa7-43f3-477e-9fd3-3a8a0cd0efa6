<!DOCTYPE html>
<html class="no-js" lang="en">

<head>
    <title>PROXYTN</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
    <meta name="viewport" content="user-scalable=no, width=device-width, height=device-height, initial-scale=1, maximum-scale=1, minimum-scale=1, minimal-ui" />

    <meta name="theme-color" content="#056EB9" />
    <meta name="msapplication-navbutton-color" content="#056EB9" />
    <meta name="apple-mobile-web-app-status-bar-style" content="#056EB9" />

    <!-- Favicons
    ================================================== -->
    <link rel="shortcut icon" href="images/favicon.ico">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="images/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="114x114" href="images/apple-touch-icon-114x114.png">

    <!-- Critical styles
    ================================================== -->
    <link rel="stylesheet" href="{{ asset('assets/css/index/critical.min.css') }}" type="text/css">

    <!-- Load google font
    ================================================== -->
    <script type="text/javascript">
        WebFontConfig = {
            google: { families: [ 'Nunito+Sans:400,400i,700,700i,800,800i,900,900i', 'Quicksand:300,400,700'] }
        };
        (function() {
            var wf = document.createElement('script');
            wf.src = ('https:' == document.location.protocol ? 'https' : 'http') + '://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js';
            wf.type = 'text/javascript';
            wf.async = 'true';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(wf, s);
        })();
    </script>

    <!-- Load other scripts
    ================================================== -->
    <script type="text/javascript">
        var _html = document.documentElement,
            isTouch = (('ontouchstart' in _html) || (navigator.msMaxTouchPoints > 0) || (navigator.maxTouchPoints));

        _html.className = _html.className.replace("no-js","js");
        _html.classList.add( isTouch ? "touch" : "no-touch");
    </script>
    <script type="text/javascript" src="{{ asset('assets/js/device.min.js') }}"></script>

</head>

<body>
<div id="app">
    <!-- start header -->
    <header id="top-bar" class="top-bar top-bar--dark" data-nav-anchor="true">
        <div class="top-bar__inner">
            <div class="container-fluid">
                <div class="row align-items-center no-gutters">
                    <a class="top-bar__logo site-logo" href="#">
                        <img class="img-fluid" src="images/site_logo/logo_2.png" width="159" height="45" alt="demo" />
                    </a>
                    <a id="top-bar__navigation-toggler" class="top-bar__navigation-toggler" href="javascript:void(0);">
                        <span></span>
                    </a>
                    <div class="top-bar__collapse">
                    <nav id="top-bar__navigation" class="top-bar__navigation" role="navigation">
                            <ul>
                                <li>
                                    <a class="nav-link" href="{{ route('home') }}#start-screen">Trang chủ</a>
                                </li>

                                <li>
                                    <a class="nav-link" href="{{ route('home') }}#proxy">Proxy</a>
                                </li>
                                <li>
                                    <a class="nav-link" href="{{ route('home') }}#vpn">VPN</a>
                                </li>
                                <li>
                                    <a class="nav-link active" href="{{ route('policy') }}">Chính sách</a>
                                </li>
                                <li>
                                    <a class="nav-link" href="{{ route('term') }}">Điều khoản</a>
                                </li>
                                <li>
                                    <a class="nav-link" href="{{ route('download.pc') }}">PC</a>
                                </li>
                                <li>
                                    <a class="nav-link" href="{{ route('download.android') }}" target="_blank" rel="noopener noreferrer">Android</a>
                                </li>
                                <li>
                                    <a class="nav-link" href="{{ route('download.ios') }}">IOS</a>
                            </ul>
                        </nav>
                        <div id="top-bar__action" class="top-bar__action">
                            <div class="d-xl-flex flex-xl-row flex-xl-wrap align-items-xl-center">


                                <div class="top-bar__auth-btns">
                                    @if(Auth::id() == 1)
                                        <a class="custom-btn custom-btn--medium custom-btn--style-3" href="{{ route('admin.balance_history.index') }}">Bảng tổng quan</a>
                                    @elseif(Auth::id() > 1)
                                        <a class="custom-btn custom-btn--medium custom-btn--style-3" href="{{ route('balance_history.index') }}">Bảng tổng quan</a>
                                    @else
                                        <a class="custom-btn custom-btn--medium custom-btn--style-3" href="{{ route('login') }}">Đăng nhập</a>
                                        <a class="custom-btn custom-btn--medium custom-btn--style-3 ml-1" href="{{ route('register') }}">Đăng ký</a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </header>
    <main role="main">
        <!-- Common  styles
        ================================================== -->
        <link rel="stylesheet" href="{{ asset('assets/css/index/style.min.css') }}" type="text/css">

        <!-- Load lazyLoad scripts
        ================================================== -->
        <script>
            (function(w, d){
                var m = d.getElementsByTagName('main')[0],
                    s = d.createElement("script"),
                    v = !("IntersectionObserver" in w) ? "8.17.0" : "11.0.6",
                    o = {
                        elements_selector: ".lazy",
                        threshold: 500,
                        callback_enter: function (element) {

                        },
                        callback_load: function (element) {

                        },
                        callback_set: function (element) {

                            oTimeout = setTimeout(function ()
                            {
                                clearTimeout(oTimeout);

                                AOS.refresh();
                            }, 1000);
                        },
                        callback_error: function(element) {
                            element.src = "https://placeholdit.imgix.net/~text?txtsize=21&txt=Image%20not%20load&w=200&h=200";
                        }
                    };
                s.type = 'text/javascript';
                s.async = false; // This includes the script as async. See the "recipes" section for more information about async loading of LazyLoad.
                s.src = "https://cdn.jsdelivr.net/npm/vanilla-lazyload@" + v + "/dist/lazyload.min.js";
                m.appendChild(s);
                // m.insertBefore(s, m.firstChild);
                w.lazyLoadOptions = o;
            }(window, document));
        </script>

        <!-- start section -->
        <section class="section">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="container">
                            <h1 class="hero__title"><span>CHÍNH SÁCH BẢO MẬT THÔNG TIN</span></h1>
                        </div>
                        <div class="content-container">
                            <hr>
                            <h3>CHÍNH SÁCH BẢO MẬT THÔNG TIN</h3>
                            <p>
                                <strong>ProxyTN</strong> chúng tôi luôn tôn trọng sự riêng tư của bạn, và đó là lý do chúng tôi luôn cam kết bảo vệ những thông tin cá nhân của bạn. Trong tài liệu này, khái niệm "thông tin cá nhân" được hiểu bao gồm tên, địa chỉ, địa chỉ email, số điện thoại hay bất kỳ thông tin cá nhân nào khác mà bạn cung cấp, hoặc bất kỳ thông tin nào về bạn được thu thập theo luật định hiện hành. Mong rằng tài liệu sẽ giúp bạn hiểu rõ những thông tin cá nhân nào mà chúng tôi sẽ thu thập, cũng như cách chúng tôi sử dụng những thông tin này sau đó.
                            </p>
                            <p>
                                Khi bạn truy cập và sử dụng trang web (bao gồm cả việc đăng ký dịch vụ trực tuyến), chúng tôi hiểu rằng bạn hoàn toàn đồng ý với những điều khoản của chính sách này.
                            </p>
                            <h4>1. Khai báo khi sử dụng</h4>
                            <p>
                                Tất cả nội dung bạn xem được trên trang web nhằm phục vụ nhu cầu thông tin của bạn và giúp bạn lựa chọn dịch vụ. Vì thế, bạn được sử dụng trang web mà không cần cung cấp bất kỳ thông tin cá nhân nào. Tuy nhiên, trong một số trường hợp, khi liên quan đến việc đăng ký dịch vụ, chúng tôi có thể yêu cầu bạn cung cấp thông tin cá nhân.
                            </p>
                            <h4>2. Thông tin cá nhân do bạn cung cấp</h4>
                            <p>
                                Khi bạn cung cấp các thông tin cần thiết, chúng tôi sẽ sử dụng chúng để đáp ứng yêu cầu của bạn, hoặc chúng tôi có thể liên lạc với bạn qua thư từ, email, tin nhắn hay điện thoại; hoặc tuân theo luật định hiện hành, sử dụng các phương tiện trên để giới thiệu đến bạn những sản phẩm, dịch vụ hay chương trình khuyến mãi mới từ ProxyTN. Khi cung cấp thông tin cho chúng tôi qua trang web này, bạn hiểu rõ và chấp thuận việc thu thập, sử dụng và tiết lộ những thông tin cá nhân nêu trên cho một số mục đích được quy định trong trang này.
                            </p>
                            <p>
                                Bạn hoàn toàn đồng ý và chấp thuận rằng những thông tin cá nhân bạn cung cấp khi sử dụng trang web có thể được bảo lưu tại trụ sở hay chi nhánh của ProxyTN, có thể được lưu trữ tại một số máy chủ hiện có hay chưa biết trước cho mục đích vận hành và phát triển trang web cũng như các dịch vụ của ProxyTN.
                            </p>
                            <p>
                                Nếu bạn đặt hàng một sản phẩm, yêu cầu một dịch vụ hoặc gởi thư phản hồi lên trang web, chúng tôi có thể liên hệ với bạn để có những thông tin bổ sung cần thiết cho việc xử lý hoặc hoàn thành đơn đặt hàng và / hoặc yêu cầu của bạn. Tuy nhiên, chúng tôi sẽ không cung cấp thông tin này cho một bên thứ ba mà không được sự cho phép của bạn, trừ trường hợp bị pháp luật hiện hành bắt buộc hoặc trường hợp cần thiết để xử lý đơn đặt hàng (ví dụ: đăng ký với VNNIC, giải quyết tranh chấp).
                            </p>
                            <p>
                                <strong>ProxyTN </strong>cam kết bảo vệ sự riêng tư của bạn, không mua bán thông tin cá nhân của bạn cho các công ty khác vì các mục đích khuyến mãi.
                            </p>
                            <h4>3. Cung cấp cho các đơn vị khác</h4>
                            <p>
                                <strong>ProxyTN </strong>có thể sử dụng một số đơn vị khác để cung cấp sản phẩm hay dịch vụ cho bạn. Chúng tôi cũng cần trao đổi thông tin cá nhân của bạn đến các đơn vị này để họ hoàn thành yêu cầu của bạn. Những đơn vị này cũng không được phép sử dụng thông tin cá nhân của bạn cho các mục đích khác và chúng tôi đồng thời yêu cầu họ tuân theo quy định bảo mật khi tiến hành cung cấp dịch vụ.
                            </p>
                            <p>
                                <strong>ProxyTN </strong>không được phép cho phép Bên thứ ba truy cập, truy xuất hay thực hiện bất thao tác nào một cách gián tiếp hay trực tiếp trên máy chủ đang cung cấp dịch vụ hoặc máy chủ của khách hàng, ngoại trừ trường hợp có yêu cầu từ cơ quan chức năng có thẩm quyền theo pháp luật Việt Nam.
                            </p>
                            <h4>4. Ghi nhận thông tin trình duyệt và dữ liệu thu thập</h4>
                            <p>
                                Đôi khi, thông tin có thể được đưa vào máy tính của bạn để giúp chúng tôi nâng cấp trang web hay cải thiện chất lượng dịch vụ cho bạn. Những thông tin này thường được biết đến dưới dạng các "cookies" mà nhiều trang web hiện cũng đang sử dụng. "Cookies" là những mẩu thông tin lưu trữ trong đĩa cứng hay trình duyệt trên máy tính của bạn, không phải trên trang web. Chúng cho phép thu thập một số thông tin về máy tính của bạn như địa chỉ IP, hệ điều hành, chế độ trình duyệt và địa chỉ của các trang web liên quan.
                            </p>
                            <p>
                                Nếu bạn không muốn nhận các cookies này, hoặc muốn được thông báo khi các cookies này được đặt vào, bạn có thể cài đặt chế độ trình duyệt của bạn thực hiện điều này nếu trình duyệt của bạn hỗ trợ. Vui lòng lưu ý, nếu bạn tắt chế độ nhận cookies, bạn sẽ không thể truy cập hay sử dụng một số tiện ích trên trang web mà không được xác định trước. Chúng tôi không cố ý hạn chế việc sử dụng của bạn trong tình huống này, đây chỉ là giới hạn trong việc lập trình và xây dựng trang web.
                            </p>
                            <p>
                                Dữ liệu thu thập khác nếu có bao gồm thông tin địa chỉ IP truy cập, thời gian truy cập, thời gian ngừng truy cập (nếu có) vào các máy chủ của chúng tôi.
                            </p>
                            <h4>5. Sự an toàn</h4>
                            <p>
                                Khi lập trình trang web, chúng tôi có thể đặt những luật định hợp lý mang tính thương mại để ngăn chặn hành vi truy cập bất hợp pháp và việc sử dụng không thích đáng các thông tin cá nhân của bạn đã gửi cho <strong>ProxyTN </strong>thông qua việc sử dụng trang web này. Nếu trang web này hỗ trợ việc giao dịch trực tuyến, nó sẽ được áp dụng một tiêu chuẩn công nghệ được gọi là SSL (Secure Sockets Layer), để bảo vệ tính bảo mật và an toàn trên đường truyền dữ liệu.
                            </p>
                            <p>
                                Vì luôn có những rủi ro liên quan đến vấn đề cung cấp dữ liệu cá nhân, cho dù là cung cấp trực tiếp, qua điện thoại hay qua mạng internet, hay qua các phương tiện kỹ thuật khác; và không có hệ thống kỹ thuật nào an toàn tuyệt đối hay chống được tất cả các "hacker" và "tamper" (người xâm nhập trái phép để lục lọi thông tin), <strong>ProxyTN </strong>luôn nỗ lực tiến hành những biện pháp đề phòng thích hợp đối với từng đặc tính của thông tin để ngăn chặn và giảm thiểu tối đa các rủi ro có thể khi bạn sử dụng trang web này.
                            </p>
                            <h4>6. Thông tin qua E-mail</h4>
                            <p>
                                Khi bạn đăng ký dịch vụ, địa chỉ e-mail của bạn cung cấp sẽ dùng làm công cụ trao đổi thông tin với bạn. Trước hết bạn hãy chắc rằng bạn cung cấp địa chỉ e-mail hữu dụng đối với bạn trong suốt quá trình sử dụng dịch vụ.
                            </p>
                            <p>
                                Chúng tôi có thể gởi những thông tin bí mật qua e-mail hay tiếp nhận yêu cầu hỗ trợ của bạn. Trong trường hợp bạn không còn sử dụng e-mail đã cung cấp, bạn phải báo cho chúng tôi để về việc thay đổi này.
                            </p>
                            <p>
                                Để bảo vệ bạn, <strong>ProxyTN </strong>có thể tạm thời ngưng tiếp nhận yêu cầu qua e-mail bạn đã cung cấp nếu nhận thấy có sự gian lận hoặc thông tin bất thường – cho đến khi chúng tôi liên hệ được với bạn để xác nhận.
                            </p>
                            <h4>7. Điều chỉnh thông tin thu thập</h4>
                            <p>
                                <strong>ProxyTN </strong>sẽ chủ động hoặc theo yêu cầu của bạn bổ sung, hiệu chỉnh hay tẩy xóa các dữ liệu thông tin cá nhân không chính xác, không đầy đủ hoặc không cập nhật khi bạn còn liên kết với hoạt động của chúng tôi.
                            </p>
                            <h4>8. Đối tượng “Trẻ vị thành niên”</h4>
                            <p><strong>ProxyTN </strong>từ chối phục vụ cá nhân còn ở độ tuổi Trẻ vị thành niên (do luật pháp địa phương mà bạn cư ngụ quy định) không được quyền mua hay tìm cách sử dụng dựa trên điều lệ hợp pháp khác trên trang web này nếu không có sự chấp thuận của ba/mẹ hay người giám hộ hợp pháp, trừ khi luật pháp địa phương có áp dụng hay cho phép.</p>
                            <h4>9. Các đường liên kết ngoài trang web</h4>
                            <p>
                                Trang web này có thể chứa các đường liên kết đến các trang web khác được đặt vào nhằm mục đích giới thiệu hoặc bổ sung thông tin liên quan để bạn tham khảo. <strong>ProxyTN </strong>không chịu trách nhiệm về nội dung hay các hành vi của bất kỳ trang web nào khác.
                            </p>
                            <h4>10. Việc thay đổi quy định</h4>
                            <p>
                                <strong>ProxyTN </strong>có thể thay đổi quy định này một cách không thường xuyên, bao gồm việc bổ sung, loại bỏ một phần nội dung hoặc tạm ngưng trang web mà không cần báo trước. Tuy nhiên, nếu quy định này được thay đổi theo hướng có thể gây bất lợi cho bạn, <strong>ProxyTN </strong>sẽ cố gắng thông báo về sự thay đổi qua e-mail bạn đã cung cấp hoặc ngay trên trang chủ.
                            </p>

                        </div>

                    </div>
                </div>
            </div>
        </section>
        <!-- end section -->
    </main>

    <!-- start footer -->
    <footer class="footer footer--s1 footer--color-light">
        <div class="" style="background-color: #2d3a49">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-md-12 col-lg-12 col-xl-12">
                        <div class="footer__item" style="justify-content: center;display: flex;">
                            <span class="__copy m-1">Copyrights 2024 © <a class="__dev" href="https://proxytn.com" target="_blank">PROXYTN</a></span>
                        </div>
                        <div class="footer__item" style="justify-content: center;display: flex;margin-top: 0;margin-bottom: 0">
                            <span class="m-1"><a href="{{ route('policy') }}">Điều khoản sử dụng</a></span>
                            <span class="m-1"><a href="{{ route('term') }}">Chính sách bảo mật</a></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- end footer -->
</div>

<div id="btn-to-top-wrap">
    <a id="btn-to-top" class="circled" href="javascript:void(0);" data-visible-offset="800"></a>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js"></script>
<script>window.jQuery || document.write('<script src="{{ asset('assets/js/jquery-2.2.4.min.js')  }}"><\/script>')</script>

<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>

<script type="text/javascript" src="{{ asset('assets/js/main.min.js') }}"></script>

</body>
</html>
