select{
    padding: 10px 0;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}
.product__shape {
    width: 5rem;
    height: 5rem;
    border: 1px solid #fff4de;
    overflow: hidden;
}

.product__img {
    height: 100%;
    width: 100%;
}
.dropdown-menu2 {
    width: 100%;
    padding: .5rem 0;
    margin: .125rem 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: .25rem;
    padding-left: 20px;
    cursor: pointer;
}
.dropdown-menu2 li,
.dropdown-menu2 a{
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.dropdown-menu2 li:active,
.dropdown-menu2 li:hover,
.dropdown-menu2 a:active,
.dropdown-menu2 a:hover{
    color: #717fe0;
}
.hide{
    display: none;
}

.wrap-num-product {
    width: 140px;
    height: 45px;
    border: 1px solid #e6e6e6;
    border-radius: 3px;
    overflow: hidden;
    float: left;
}

.btn-num-product-up,
.btn-num-product-down {
    width: 45px;
    height: 100%;
    cursor: pointer;
}

.num-product {
    width: calc(100% - 90px);
    height: 100%;
    border-left: 1px solid #e6e6e6;
    border-right: 1px solid #e6e6e6;
    background-color: #f7f7f7;
}

input.num-product {
    -moz-appearance: textfield;
    appearance: none;
    -webkit-appearance: none;
    outline: none;
    border: none;
}

input.num-product::-webkit-outer-spin-button,
input.num-product::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
    outline: none;
    border: none;
}
.hov-btn3:hover {
    border-color: #717fe0;
    background-color: #717fe0;
    color: #fff;
}

.hov-btn3:hover i {
    color: #fff;
}

.flex-w,.flex-c-m {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    -ms-align-items: center;
    align-items: center;
}
.txt-center {text-align: center;}
.flex-w {
    -webkit-flex-wrap: wrap;
    -moz-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    -o-flex-wrap: wrap;
    flex-wrap: wrap;
}
@-webkit-keyframes loader-1-outter {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes loader-1-outter {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-webkit-keyframes loader-1-inner {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(-360deg);
        transform: rotate(-360deg);
    }
}

@keyframes loader-1-inner {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(-360deg);
        transform: rotate(-360deg);
    }
}
.preloading {
    width: 100%;
    height: 100%;
    background: #EEF0F8;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    display: block;
    overflow: hidden;
}
.loader {
    position: absolute;
    top: 50%;
    left: 50%;
}
.loader-1 .loader-outter {
    position: absolute;
    border: 4px solid #00BFFF;
    border-left-color: transparent;
    border-bottom: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    -webkit-animation: loader-1-outter 1s cubic-bezier(.42, .61, .58, .41) infinite;
    animation: loader-1-outter 1s cubic-bezier(.42, .61, .58, .41) infinite;
}

.loader-1 .loader-inner {
    position: absolute;
    border: 4px solid #00BFFF;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    left: calc(50% - 20px);
    top: calc(50% - 20px);
    border-right: 0;
    border-top-color: transparent;
    -webkit-animation: loader-1-inner 1s cubic-bezier(.42, .61, .58, .41) infinite;
    animation: loader-1-inner 1s cubic-bezier(.42, .61, .58, .41) infinite;
}

.card-header-custom {
    border-bottom: 1px solid rgba(10,2,30,.125);
}
