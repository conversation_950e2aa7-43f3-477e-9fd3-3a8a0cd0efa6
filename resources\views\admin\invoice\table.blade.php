<div class="card-body">
    {!!
        $dataTable->table([
            'class' => 'table table-separate table-head-custom table-checkable display nowrap',
            'id' => 'datatable_invoice',
        ])
    !!}
</div>
@push('scripts')
    {!! $dataTable->scripts() !!}
    <script type="text/javascript">
        let DatatableInvoice;
        $(document).ready(function() {
            DatatableInvoice = window.LaravelDataTables["datatable_invoice"];
        });
        $(document).on('click', '.status_invoice', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: '{{__('Bạn có chắc chắn muốn xác nhận đơn hàng này thanh toán thành công không?')}}',
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.put('{{ route('admin.invoice.index') }}/status/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.message)
                                DatatableInvoice.ajax.reload(null, false);
                            } else {
                                mess_error(response.data.message)
                            }
                        });
                }
            });
        });
        $(document).on('click', '.block_invoice', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                icon: "question",
                title: '{{__('Câu hỏi')}}',
                text: '{{__('Bạn có chắc chắn muốn từ chối đơn hàng này không?')}}',
                showCancelButton: true,
                confirmButtonText: '{{__('Đồng ý')}}',
                cancelButtonText: '{{__('Không')}}',
            }).then(function (result) {
                if (result.value) {
                    axios.put('{{ route('admin.invoice.index') }}/block-status/' + id)
                        .then((response) => {
                            if (response.data.status) {
                                mess_success(response.data.message)
                                DatatableInvoice.ajax.reload(null, false);
                            } else {
                                mess_error(response.data.message)
                            }
                        });
                }
            });
        });
    </script>
@endpush
